import React from 'react';
import ModernButton from './ModernButton';
import { X, Plus, Edit2 } from 'lucide-react';

const BahanBakuModal = ({
  isOpen,
  onClose,
  formData,
  setFormData,
  handleSubmit,
  editingIngredient,
}) => {
  const units = [
    { value: 'g', label: 'Gram' },
    { value: 'kg', label: 'Kilogram' },
    { value: 'pcs', label: 'Pcs' },
  ];

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
        <div className="border-b p-4 flex justify-between items-center">
          <h2 className="text-xl font-semibold">
            {editingIngredient ? 'Edit Bahan Baku' : 'Tambah Bahan Baku'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <div className="p-6">
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Nama Bahan
              </label>
              <input
                type="text"
                name="nama"
                value={formData.nama}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  nama: e.target.value
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Berat
              </label>
              <input
                type="number"
                name="berat"
                value={formData.berat}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  berat: parseFloat(e.target.value) || 0
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Harga Total (Rp)
              </label>
              <input
                type="number"
                name="harga"
                value={formData.harga}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  harga: parseFloat(e.target.value) || 0
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Satuan
              </label>
              <select
                name="satuan"
                value={formData.satuan}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  satuan: e.target.value
                }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {units.map((unit) => (
                  <option key={unit.value} value={unit.value}>
                    {unit.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div className="mt-6 flex justify-end space-x-3">
            <ModernButton
              type="button"
              color="inherit"
              style={{ minWidth: 90, fontWeight: 500 }}
              onClick={onClose}
            >
              Batal
            </ModernButton>
            <ModernButton
              type="button"
              color="primary"
              style={{ minWidth: 90, fontWeight: 600 }}
              onClick={handleSubmit}
            >
              {editingIngredient ? 'Simpan' : 'Tambah'}
            </ModernButton>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BahanBakuModal;
