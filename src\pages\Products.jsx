import React, { useState, useEffect } from 'react'
import { Plus, Edit, Trash2, Package } from 'lucide-react'
import { getProducts, saveProduct, updateProduct, deleteProduct } from '../services/storage'
import { useGlobalDialog } from '../contexts/DialogContext'
import ModernButton from '../components/ModernButton'

const Products = () => {
  const [products, setProducts] = useState([])
  const [search, setSearch] = useState("")
  const [showModal, setShowModal] = useState(false)
  const [editingProduct, setEditingProduct] = useState(null)
  const [formData, setFormData] = useState({
    nama: '',
    harga: ''
  })
  const { deleteConfirm, success, error: showError } = useGlobalDialog()

  useEffect(() => {
    loadProducts()
  }, [])

  const loadProducts = () => {
    const data = getProducts()
    setProducts(data)
  }

  const handleSubmit = async (e) => {
    e.preventDefault()

    const productData = {
      nama: formData.nama,
      harga: parseFloat(formData.harga)
    }

    try {
      if (editingProduct) {
        updateProduct(editingProduct.id, productData)
        await success({
          title: 'Berhasil!',
          message: 'Produk berhasil diperbarui!'
        })
      } else {
        saveProduct(productData)
        await success({
          title: 'Berhasil!',
          message: 'Produk berhasil ditambahkan!'
        })
      }

      setFormData({ nama: '', harga: '' })
      setEditingProduct(null)
      setShowModal(false)
      loadProducts()
    } catch (error) {
      await showError({
        title: 'Error!',
        message: 'Terjadi kesalahan saat menyimpan produk.'
      })
    }
  }

  const handleEdit = (product) => {
    setEditingProduct(product)
    setFormData({
      nama: product.nama,
      harga: product.harga.toString()
    })
    setShowModal(true)
  }

  const handleDelete = async (product) => {
    const confirmed = await deleteConfirm({
      title: 'Hapus Produk',
      message: `Apakah Anda yakin ingin menghapus produk "${product.nama}"?\n\nTindakan ini tidak dapat dibatalkan.`,
      confirmText: 'Hapus',
      cancelText: 'Batal'
    })

    if (confirmed) {
      try {
        deleteProduct(product.id)
        await success({
          title: 'Berhasil!',
          message: 'Produk berhasil dihapus!'
        })
        loadProducts()
      } catch (error) {
        await showError({
          title: 'Error!',
          message: 'Terjadi kesalahan saat menghapus produk.'
        })
      }
    }
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR'
    }).format(amount)
  }

  return (
    <div className="space-y-4 px-2" style={{maxWidth: 800, margin: '0 auto', width: '100%'}}>
      {/* Header */}
      <div className="flex flex-col gap-2 w-full">
        <h1 className="text-lg font-bold w-full" style={{color: 'var(--theme-500, #34d399)', wordBreak: 'break-word'}}>Manajemen Produk</h1>
        <ModernButton
  onClick={() => {
    setEditingProduct(null)
    setFormData({ nama: '', harga: '', deskripsi: '' })
    setShowModal(true)
  }}
  startIcon={<Plus className="h-5 w-5 sm:h-6 sm:w-6" />}
  fullWidth
  size="large"
  style={{ fontWeight: 700, fontSize: 16, wordBreak: 'break-word', letterSpacing: 0.5, padding: '14px 0', borderRadius: 14 }}
>
  Tambah Produk
</ModernButton>
      </div>

      {/* Search Bar */}
      <div className="mb-4 flex w-full">
        <input
          type="text"
          placeholder="Cari produk..."
          value={search}
          onChange={e => setSearch(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-400 text-xs"
          style={{fontSize: 13, lineHeight: 1.2}}
        />
      </div>
      {/* Total Records */}
<div className="mb-2 text-sm font-semibold" style={{ color: 'var(--theme-700, #374151)' }}>
  Total Produk: {products.filter(product => product.nama.toLowerCase().includes(search.toLowerCase())).length}
</div>
{/* Products List */}
      <div className="card">
        {products.filter(product => product.nama.toLowerCase().includes(search.toLowerCase())).length > 0 ? (
          <>
            {/* Desktop Table */}
            <div className="hidden md:block overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gradient-to-r from-gray-50 to-gray-100">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Nama Produk
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Harga
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Aksi
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {products.filter(product => product.nama.toLowerCase().includes(search.toLowerCase())).map((product) => (
                    <tr key={product.id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{product.nama}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{formatCurrency(product.harga)}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex justify-end space-x-2">
                          <ModernButton
                            onClick={() => handleEdit(product)}
                            color="success"
                            size="small"
                            className="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded-full text-xs flex items-center space-x-1"
                            startIcon={<Edit className="h-3 w-3" />}
                          >
                            Edit
                          </ModernButton>
                          <ModernButton
                            onClick={() => handleDelete(product)}
                            color="error"
                            size="small"
                            className="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded-full text-xs flex items-center space-x-1"
                            startIcon={<Trash2 className="h-3 w-3" />}
                          >
                            Hapus
                          </ModernButton>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Mobile Cards */}
            <div className="md:hidden p-4 space-y-4">
              {products.filter(product => product.nama.toLowerCase().includes(search.toLowerCase())).map((product) => (
                <div key={product.id} className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900">{product.nama}</h3>
                      <p className="text-sm text-gray-600">{formatCurrency(product.harga)}</p>
                    </div>
                    <div className="flex space-x-2 ml-4">
                      <ModernButton
                        onClick={() => handleEdit(product)}
                        color="success"
                        size="small"
                        className="bg-green-500 hover:bg-green-600 text-white px-2 py-1 rounded-full text-xs flex items-center space-x-1"
                        startIcon={<Edit className="h-3 w-3" />}
                      >
                        Edit
                      </ModernButton>
                      <ModernButton
                        onClick={() => handleDelete(product)}
                        color="error"
                        size="small"
                        className="bg-red-500 hover:bg-red-600 text-white px-2 py-1 rounded-full text-xs flex items-center space-x-1"
                        startIcon={<Trash2 className="h-3 w-3" />}
                      >
                        Hapus
                      </ModernButton>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </>
        ) : (
          <div className="text-center py-12">
            <Package className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Belum ada produk</h3>
            <p className="mt-1 text-sm text-gray-500">
              Mulai dengan menambahkan produk pertama Anda.
            </p>
            <div className="mt-6">
              <ModernButton
  onClick={() => {
    setEditingProduct(null)
    setFormData({ nama: '', harga: '', deskripsi: '' })
    setShowModal(true)
  }}
  startIcon={<Plus className="h-5 w-5 sm:h-6 sm:w-6" />}
  fullWidth
  size="large"
  style={{ fontWeight: 700, fontSize: 16, wordBreak: 'break-word', letterSpacing: 0.5, padding: '14px 0', borderRadius: 14 }}
>
  Tambah Produk
</ModernButton>
            </div>
          </div>
        )}
      </div>

      {/* Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-2 border w-full max-w-md shadow-2xl rounded-xl bg-white" style={{minWidth: 0}}>
            <div className="mt-3">
              <h3 className="text-base font-semibold text-gray-900 mb-4" style={{lineHeight: 1.2}}>
                {editingProduct ? 'Edit Produk' : 'Tambah Produk'}
              </h3>
              <form onSubmit={handleSubmit} className="space-y-3">
                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">Nama Produk</label>
                  <input
                    type="text"
                    required
                    value={formData.nama}
                    onChange={(e) => setFormData({ ...formData, nama: e.target.value })}
                    className="input-field w-full text-xs"
                    style={{fontSize: 13, padding: '8px 8px'}}
                  />
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">Harga</label>
                  <input
                    type="number"
                    required
                    min="0"
                    step="0.01"
                    value={formData.harga}
                    onChange={(e) => setFormData({ ...formData, harga: e.target.value })}
                    className="input-field w-full text-xs"
                    style={{fontSize: 13, padding: '8px 8px'}}
                  />
                </div>
                <div className="flex flex-col sm:flex-row justify-end gap-2 pt-4 w-full">
                  <ModernButton
                    type="button"
                    onClick={() => setShowModal(false)}
                    color="inherit"
                    fullWidth
                    style={{ minWidth: 90, fontSize: 13 }}
                  >
                    Batal
                  </ModernButton>
                  <ModernButton
                    type="submit"
                    color="primary"
                    fullWidth
                    style={{ minWidth: 90, fontSize: 13 }}
                  >
                    {editingProduct ? 'Update' : 'Simpan'}
                  </ModernButton>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default Products
