import React, { useState, useEffect } from 'react'
import { Plus, Edit, Trash2, Package } from 'lucide-react'
import { getProducts, saveProduct, updateProduct, deleteProduct } from '../services/storage'
import { useGlobalDialog } from '../contexts/DialogContext'
import ModernButton from '../components/ModernButton'

const Products = () => {
  const [products, setProducts] = useState([])
  const [search, setSearch] = useState("")
  const [showModal, setShowModal] = useState(false)
  const [editingProduct, setEditingProduct] = useState(null)
  const [formData, setFormData] = useState({
    nama: '',
    harga: ''
  })
  const { deleteConfirm, success, error: showError } = useGlobalDialog()

  useEffect(() => {
    loadProducts()
  }, [])

  const loadProducts = () => {
    const data = getProducts()
    setProducts(data)
  }

  const handleSubmit = async (e) => {
    e.preventDefault()

    const productData = {
      nama: formData.nama,
      harga: parseFloat(formData.harga)
    }

    try {
      if (editingProduct) {
        updateProduct(editingProduct.id, productData)
        await success({
          title: 'Berhasil!',
          message: 'Produk berhasil diperbarui!'
        })
      } else {
        saveProduct(productData)
        await success({
          title: 'Berhasil!',
          message: 'Produk berhasil ditambahkan!'
        })
      }

      setFormData({ nama: '', harga: '' })
      setEditingProduct(null)
      setShowModal(false)
      loadProducts()
    } catch (error) {
      await showError({
        title: 'Error!',
        message: 'Terjadi kesalahan saat menyimpan produk.'
      })
    }
  }

  const handleEdit = (product) => {
    setEditingProduct(product)
    setFormData({
      nama: product.nama,
      harga: product.harga.toString()
    })
    setShowModal(true)
  }

  const handleDelete = async (product) => {
    const confirmed = await deleteConfirm({
      title: 'Hapus Produk',
      message: `Apakah Anda yakin ingin menghapus produk "${product.nama}"?\n\nTindakan ini tidak dapat dibatalkan.`,
      confirmText: 'Hapus',
      cancelText: 'Batal'
    })

    if (confirmed) {
      try {
        deleteProduct(product.id)
        await success({
          title: 'Berhasil!',
          message: 'Produk berhasil dihapus!'
        })
        loadProducts()
      } catch (error) {
        await showError({
          title: 'Error!',
          message: 'Terjadi kesalahan saat menghapus produk.'
        })
      }
    }
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR'
    }).format(amount)
  }

  const formatPrice = (amount) => {
    return new Intl.NumberFormat('id-ID').format(amount)
  }

  return (
    <div className="space-y-4 px-2" style={{maxWidth: 800, margin: '0 auto', width: '100%'}}>
      {/* Header */}
      <div className="flex flex-col gap-2 w-full">
        <h1 className="text-lg font-bold w-full" style={{color: 'var(--theme-500, #34d399)', wordBreak: 'break-word'}}>Manajemen Produk</h1>
        <ModernButton
  onClick={() => {
    setEditingProduct(null)
    setFormData({ nama: '', harga: '', deskripsi: '' })
    setShowModal(true)
  }}
  startIcon={<Plus className="h-5 w-5 sm:h-6 sm:w-6" />}
  fullWidth
  size="large"
  style={{ fontWeight: 700, fontSize: 16, wordBreak: 'break-word', letterSpacing: 0.5, padding: '14px 0', borderRadius: 14 }}
>
  Tambah Produk
</ModernButton>
      </div>

      {/* Search Bar */}
      <div className="mb-4 flex w-full">
        <input
          type="text"
          placeholder="Cari produk..."
          value={search}
          onChange={e => setSearch(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-400 text-xs"
          style={{fontSize: 13, lineHeight: 1.2}}
        />
      </div>
      {/* Total Records */}
<div className="mb-2 text-sm font-semibold" style={{ color: 'var(--theme-700, #374151)' }}>
  Total Produk: {products.filter(product => product.nama.toLowerCase().includes(search.toLowerCase())).length}
</div>
{/* Products List */}
      <div className="card">
        {products.filter(product => product.nama.toLowerCase().includes(search.toLowerCase())).length > 0 ? (
          <>
            {/* Desktop Table */}
            <div className="hidden md:block">
              <div className="table-responsive">
                <table className="table table-striped table-hover table-sm">
                  <thead className="table-light">
                    <tr>
                      <th scope="col" className="py-2">Nama Produk</th>
                      <th scope="col" className="py-2">Harga</th>
                      <th scope="col" className="text-end py-2">Aksi</th>
                    </tr>
                  </thead>
                  <tbody>
                    {products.filter(product => product.nama.toLowerCase().includes(search.toLowerCase())).map((product) => (
                      <tr key={product.id}>
                        <td className="fw-medium py-2 align-middle text-nowrap">{product.nama}</td>
                        <td className="text-muted py-2 align-middle text-nowrap small">{formatPrice(product.harga)}</td>
                        <td className="text-end py-2 align-middle">
                          <div className="d-flex justify-content-end gap-1">
                            <button
                              onClick={() => handleEdit(product)}
                              className="btn btn-outline-success btn-sm d-flex align-items-center justify-content-center p-1"
                              style={{ width: '28px', height: '28px' }}
                              title="Edit"
                            >
                              <Edit style={{ width: '14px', height: '14px' }} />
                            </button>
                            <button
                              onClick={() => handleDelete(product)}
                              className="btn btn-outline-danger btn-sm d-flex align-items-center justify-content-center p-1"
                              style={{ width: '28px', height: '28px' }}
                              title="Hapus"
                            >
                              <Trash2 style={{ width: '14px', height: '14px' }} />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Mobile Table */}
            <div className="d-md-none">
              <div className="card-header bg-light">
                <h6 className="card-title mb-0 fw-semibold text-dark">
                  Daftar Produk ({products.filter(product => product.nama.toLowerCase().includes(search.toLowerCase())).length})
                </h6>
              </div>
              <div className="card-body p-0">
                <div className="p-3 bg-light border-bottom">
                  <div className="row text-center">
                    <div className="col-5">
                      <small className="fw-semibold text-muted text-uppercase">Nama Produk</small>
                    </div>
                    <div className="col-4">
                      <small className="fw-semibold text-muted text-uppercase">Harga</small>
                    </div>
                    <div className="col-3">
                      <small className="fw-semibold text-muted text-uppercase">Act</small>
                    </div>
                  </div>
                </div>
                {products.filter(product => product.nama.toLowerCase().includes(search.toLowerCase())).map((product) => (
                  <div key={product.id} className="border-bottom border-light">
                    <div className="p-3">
                      <div className="row align-items-center">
                        <div className="col-5">
                          <span className="fw-medium text-dark small text-truncate d-block">{product.nama}</span>
                        </div>
                        <div className="col-4">
                          <span className="text-muted small">
                            {formatPrice(product.harga)}
                          </span>
                        </div>
                        <div className="col-3">
                          <div className="d-flex gap-1 justify-content-center">
                            <button
                              onClick={() => handleEdit(product)}
                              className="btn btn-outline-success btn-sm d-flex align-items-center justify-content-center p-1"
                              style={{ width: '22px', height: '22px' }}
                              title="Edit"
                            >
                              <Edit style={{ width: '11px', height: '11px' }} />
                            </button>
                            <button
                              onClick={() => handleDelete(product)}
                              className="btn btn-outline-danger btn-sm d-flex align-items-center justify-content-center p-1"
                              style={{ width: '22px', height: '22px' }}
                              title="Hapus"
                            >
                              <Trash2 style={{ width: '11px', height: '11px' }} />
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </>
        ) : (
          <div className="text-center py-12">
            <Package className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Belum ada produk</h3>
            <p className="mt-1 text-sm text-gray-500">
              Mulai dengan menambahkan produk pertama Anda.
            </p>
            <div className="mt-6">
              <ModernButton
  onClick={() => {
    setEditingProduct(null)
    setFormData({ nama: '', harga: '', deskripsi: '' })
    setShowModal(true)
  }}
  startIcon={<Plus className="h-5 w-5 sm:h-6 sm:w-6" />}
  fullWidth
  size="large"
  style={{ fontWeight: 700, fontSize: 16, wordBreak: 'break-word', letterSpacing: 0.5, padding: '14px 0', borderRadius: 14 }}
>
  Tambah Produk
</ModernButton>
            </div>
          </div>
        )}
      </div>

      {/* Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-2 border w-full max-w-md shadow-2xl rounded-xl bg-white" style={{minWidth: 0}}>
            <div className="mt-3">
              <h3 className="text-base font-semibold text-gray-900 mb-4" style={{lineHeight: 1.2}}>
                {editingProduct ? 'Edit Produk' : 'Tambah Produk'}
              </h3>
              <form onSubmit={handleSubmit} className="space-y-3">
                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">Nama Produk</label>
                  <input
                    type="text"
                    required
                    value={formData.nama}
                    onChange={(e) => setFormData({ ...formData, nama: e.target.value })}
                    className="input-field w-full text-xs"
                    style={{fontSize: 13, padding: '8px 8px'}}
                  />
                </div>
                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">Harga</label>
                  <input
                    type="number"
                    required
                    min="0"
                    step="0.01"
                    value={formData.harga}
                    onChange={(e) => setFormData({ ...formData, harga: e.target.value })}
                    className="input-field w-full text-xs"
                    style={{fontSize: 13, padding: '8px 8px'}}
                  />
                </div>
                <div className="flex flex-col sm:flex-row justify-end gap-2 pt-4 w-full">
                  <ModernButton
                    type="button"
                    onClick={() => setShowModal(false)}
                    color="inherit"
                    fullWidth
                    style={{ minWidth: 90, fontSize: 13 }}
                  >
                    Batal
                  </ModernButton>
                  <ModernButton
                    type="submit"
                    color="primary"
                    fullWidth
                    style={{ minWidth: 90, fontSize: 13 }}
                  >
                    {editingProduct ? 'Update' : 'Simpan'}
                  </ModernButton>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default Products
