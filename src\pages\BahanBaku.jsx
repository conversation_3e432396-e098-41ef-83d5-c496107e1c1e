import React, { useState, useEffect } from "react";
import { Plus, Search, Edit2, Trash2 } from "lucide-react";
import { formatRupiah } from '../utils/formatting';
import { ingredientService } from "../services/ingredientService";
import BahanBakuModal from '../components/BahanBakuModal';
import ModernButton from '../components/ModernButton';

const BahanBaku = () => {
  const [ingredients, setIngredients] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [editingIngredient, setEditingIngredient] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [formData, setFormData] = useState({
    nama: "",
    berat: 0,
    harga: 0,
    satuan: "g",
  });

  useEffect(() => {
    loadIngredients();
  }, []);

  const loadIngredients = () => {
    const allIngredients = ingredientService.getAllIngredients();
    const filteredIngredients = ingredientService.searchIngredients(searchQuery);
    setIngredients(filteredIngredients);
  };

  const handleDeleteIngredient = (id) => {
    if (window.confirm("Are you sure you want to delete this ingredient?")) {
      ingredientService.deleteIngredient(id);
      loadIngredients();
    }
  };

  const handleEditIngredient = (ingredient) => {
    setEditingIngredient(ingredient);
    setFormData(ingredient);
    setIsModalOpen(true);
  };

  const handleSearch = (e) => {
    setSearchQuery(e.target.value);
    loadIngredients();
  };

  // Available units for ingredients
  const units = [
    { value: 'g', label: 'Gram' },
    { value: 'kg', label: 'Kilogram' },
    { value: 'pcs', label: 'Pcs' },
  ];

  return (
    <div className="max-w-[100vw] overflow-hidden">
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-gray-900">Bahan Baku</h1>
          <ModernButton
            onClick={() => setIsModalOpen(true)}
            startIcon={<Plus className="h-4 w-4 mr-2" />}
            color="primary"
            size="medium"
            style={{ fontWeight: 600, fontSize: 15, borderRadius: 10 }}
          >
            Tambah Bahan Baku
          </ModernButton>
        </div>

        {/* Search */}
        <div className="mb-6">
          <div className="relative">
            <input
              type="text"
              placeholder="Cari bahan baku..."
              value={searchQuery}
              onChange={handleSearch}
              className="w-full px-4 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <Search className="absolute right-3 top-3 w-5 h-5 text-gray-400" />
          </div>
        </div>

        {/* Desktop Table */}
        <div className="hidden md:block overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gradient-to-r from-gray-50 to-gray-100">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Nama Bahan Baku
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Berat
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Harga
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Aksi
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {ingredients.map((ingredient) => (
                <tr key={ingredient.id} className="hover:bg-gray-50 transition-colors">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{ingredient.nama}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{ingredient.berat} {ingredient.satuan}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-semibold text-emerald-600">Rp {formatRupiah(ingredient.harga)}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <ModernButton
                      onClick={() => handleEditIngredient(ingredient)}
                      color="success"
                      size="small"
                      style={{ minWidth: 32, minHeight: 32, padding: 0, background: 'transparent', boxShadow: 'none', marginRight: 6 }}
                      startIcon={<Edit2 className="h-4 w-4" />}
                    />
                    <ModernButton
                      onClick={() => handleDeleteIngredient(ingredient.id)}
                      color="error"
                      size="small"
                      style={{ minWidth: 32, minHeight: 32, padding: 0, background: 'transparent', boxShadow: 'none' }}
                      startIcon={<Trash2 className="h-4 w-4" />}
                    />
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Mobile Cards */}
        <div className="md:hidden p-4 space-y-4">
          {ingredients.map((ingredient) => (
            <div key={ingredient.id} className="bg-gray-50 rounded-lg p-4 border border-gray-200">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900">{ingredient.nama}</h3>
                  <p className="text-sm text-gray-800">{ingredient.berat} {ingredient.satuan}</p>
                  <p className="text-lg font-bold text-emerald-600 mt-1">Rp {formatRupiah(ingredient.harga)}</p>
                </div>
                <div className="flex space-x-2 ml-4">
                  <ModernButton
                    onClick={() => handleEditIngredient(ingredient)}
                    color="success"
                    size="small"
                    style={{ minWidth: 32, minHeight: 32, padding: 0, background: 'transparent', boxShadow: 'none', marginRight: 6 }}
                    startIcon={<Edit2 className="h-4 w-4" />}
                  />
                  <ModernButton
                    onClick={() => handleDeleteIngredient(ingredient.id)}
                    color="error"
                    size="small"
                    style={{ minWidth: 32, minHeight: 32, padding: 0, background: 'transparent', boxShadow: 'none' }}
                    startIcon={<Trash2 className="h-4 w-4" />}
                  />
                </div>
              </div>
            </div>
          ))}
        </div>

        <BahanBakuModal
          isOpen={isModalOpen}
          onClose={() => {
            setIsModalOpen(false);
            setEditingIngredient(null);
            setFormData({ nama: "", berat: 0, harga: 0, satuan: "g" });
          }}
          formData={formData}
          setFormData={setFormData}
          handleSubmit={() => {
            if (!formData.nama || !formData.berat || !formData.harga) return;
            
            if (editingIngredient) {
              ingredientService.updateIngredient(editingIngredient.id, formData);
              setEditingIngredient(null);
            } else {
              ingredientService.addIngredient(formData);
            }
            
            setFormData({ nama: "", berat: 0, harga: 0, satuan: "g" });
            setIsModalOpen(false);
            loadIngredients();
          }}
          editingIngredient={editingIngredient}
        />
      </div>
    </div>
  );
};

export default BahanBaku;
