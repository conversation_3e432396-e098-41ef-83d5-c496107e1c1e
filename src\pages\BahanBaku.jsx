import React, { useState, useEffect } from "react";
import { Plus, Search, Edit2, Trash2 } from "lucide-react";
import { formatRupiah } from '../utils/formatting';
import { ingredientService } from "../services/ingredientService";
import BahanBakuModal from '../components/BahanBakuModal';
import ModernButton from '../components/ModernButton';

const BahanBaku = () => {
  const [ingredients, setIngredients] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [editingIngredient, setEditingIngredient] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [formData, setFormData] = useState({
    nama: "",
    berat: 0,
    harga: 0,
    satuan: "g",
  });

  useEffect(() => {
    loadIngredients();
  }, []);

  const loadIngredients = () => {
    const allIngredients = ingredientService.getAllIngredients();
    const filteredIngredients = ingredientService.searchIngredients(searchQuery);
    setIngredients(filteredIngredients);
  };

  const handleDeleteIngredient = (id) => {
    if (window.confirm("Are you sure you want to delete this ingredient?")) {
      ingredientService.deleteIngredient(id);
      loadIngredients();
    }
  };

  const handleEditIngredient = (ingredient) => {
    setEditingIngredient(ingredient);
    setFormData(ingredient);
    setIsModalOpen(true);
  };

  const handleSearch = (e) => {
    setSearchQuery(e.target.value);
    loadIngredients();
  };

  // Available units for ingredients
  const units = [
    { value: 'g', label: 'Gram' },
    { value: 'kg', label: 'Kilogram' },
    { value: 'pcs', label: 'Pcs' },
  ];

  return (
    <div className="max-w-[100vw] overflow-hidden">
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-gray-900">Bahan Baku</h1>
          <ModernButton
            onClick={() => setIsModalOpen(true)}
            startIcon={<Plus className="h-4 w-4 mr-2" />}
            color="primary"
            size="medium"
            style={{ fontWeight: 600, fontSize: 15, borderRadius: 10 }}
          >
            Tambah Bahan Baku
          </ModernButton>
        </div>

        {/* Search */}
        <div className="mb-6">
          <div className="relative">
            <input
              type="text"
              placeholder="Cari bahan baku..."
              value={searchQuery}
              onChange={handleSearch}
              className="w-full px-4 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <Search className="absolute right-3 top-3 w-5 h-5 text-gray-400" />
          </div>
        </div>

        {/* Desktop Table */}
        <div className="hidden md:block">
          <div className="table-responsive">
            <table className="table table-striped table-hover">
              <thead className="table-light">
                <tr>
                  <th scope="col">Nama Bahan Baku</th>
                  <th scope="col">Berat</th>
                  <th scope="col">Harga</th>
                  <th scope="col" className="text-end">Aksi</th>
                </tr>
              </thead>
              <tbody>
                {ingredients.map((ingredient) => (
                  <tr key={ingredient.id}>
                    <td className="fw-medium">{ingredient.nama}</td>
                    <td>{ingredient.berat} {ingredient.satuan}</td>
                    <td className="fw-semibold text-success">Rp {formatRupiah(ingredient.harga)}</td>
                    <td className="text-end">
                      <div className="d-flex justify-content-end gap-2">
                        <button
                          onClick={() => handleEditIngredient(ingredient)}
                          className="btn btn-outline-success btn-sm d-flex align-items-center justify-content-center"
                          style={{ width: '32px', height: '32px' }}
                        >
                          <Edit2 className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteIngredient(ingredient.id)}
                          className="btn btn-outline-danger btn-sm d-flex align-items-center justify-content-center"
                          style={{ width: '32px', height: '32px' }}
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Mobile Cards */}
        <div className="d-md-none p-3">
          {ingredients.map((ingredient) => (
            <div key={ingredient.id} className="card mb-3 border-light">
              <div className="card-body">
                <div className="d-flex justify-content-between align-items-start">
                  <div className="flex-grow-1">
                    <h5 className="card-title fw-semibold mb-1">{ingredient.nama}</h5>
                    <p className="card-text text-muted small mb-1">{ingredient.berat} {ingredient.satuan}</p>
                    <p className="card-text fw-bold text-success mb-0">Rp {formatRupiah(ingredient.harga)}</p>
                  </div>
                  <div className="d-flex gap-2 ms-3">
                    <button
                      onClick={() => handleEditIngredient(ingredient)}
                      className="btn btn-outline-success btn-sm d-flex align-items-center justify-content-center"
                      style={{ width: '32px', height: '32px' }}
                    >
                      <Edit2 className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleDeleteIngredient(ingredient.id)}
                      className="btn btn-outline-danger btn-sm d-flex align-items-center justify-content-center"
                      style={{ width: '32px', height: '32px' }}
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        <BahanBakuModal
          isOpen={isModalOpen}
          onClose={() => {
            setIsModalOpen(false);
            setEditingIngredient(null);
            setFormData({ nama: "", berat: 0, harga: 0, satuan: "g" });
          }}
          formData={formData}
          setFormData={setFormData}
          handleSubmit={() => {
            if (!formData.nama || !formData.berat || !formData.harga) return;
            
            if (editingIngredient) {
              ingredientService.updateIngredient(editingIngredient.id, formData);
              setEditingIngredient(null);
            } else {
              ingredientService.addIngredient(formData);
            }
            
            setFormData({ nama: "", berat: 0, harga: 0, satuan: "g" });
            setIsModalOpen(false);
            loadIngredients();
          }}
          editingIngredient={editingIngredient}
        />
      </div>
    </div>
  );
};

export default BahanBaku;
