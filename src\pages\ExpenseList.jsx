import React, { useState, useEffect } from 'react';
import { Plus, Edit2, Trash2, Search, DollarSign, Filter, X } from 'lucide-react';
import ModernButton from '../components/ModernButton';

const periodOptions = [
  { value: 'day', label: 'Hari Ini' },
  { value: 'week', label: 'Minggu Ini' },
  { value: 'month', label: 'Bulan Ini' },
  { value: 'year', label: 'Tahun Ini' },
];

const ExpenseList = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('month');
  const [expenses, setExpenses] = useState([]);
  const [search, setSearch] = useState('');
  const [form, setForm] = useState({
    id: null,
    name: '',
    date: '',
    amount: ''
  });
  const [isEditing, setIsEditing] = useState(false);
  const [showModal, setShowModal] = useState(false);

  // Helper: parse date string to Date object (YYYY-MM-DD)
  const parseDate = (dateStr) => {
    if (!dateStr) return null;
    // Accepts YYYY-MM-DD or YYYY/MM/DD
    const [y, m, d] = dateStr.split(/[-\/]/);
    return new Date(Number(y), Number(m) - 1, Number(d));
  };

  // Filter expenses by selected period
  function filterExpensesByPeriod(expenses, period) {
    if (!Array.isArray(expenses)) return [];
    const now = new Date();
    let start, end;
    switch (period) {
      case 'day':
        start = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        end = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
        break;
      case 'week': {
        const day = now.getDay() || 7;
        start = new Date(now);
        start.setDate(now.getDate() - day + 1);
        start.setHours(0, 0, 0, 0);
        end = new Date(start);
        end.setDate(start.getDate() + 7);
        break;
      }
      case 'month':
        start = new Date(now.getFullYear(), now.getMonth(), 1);
        end = new Date(now.getFullYear(), now.getMonth() + 1, 1);
        break;
      case 'year':
        start = new Date(now.getFullYear(), 0, 1);
        end = new Date(now.getFullYear() + 1, 0, 1);
        break;
      default:
        start = null;
        end = null;
    }
    return expenses.filter(exp => {
      if (!exp.date) return false;
      const d = parseDate(exp.date);
      if (start && d < start) return false;
      if (end && d >= end) return false;
      return true;
    });
  }

  const filteredExpenses = filterExpensesByPeriod(expenses, selectedPeriod);

  // Calculate total for filtered expenses
  const totalFiltered = filteredExpenses.reduce((sum, exp) => sum + (exp.amount || 0), 0);

  // Helper: get start/end of today, week, month, year
  const now = new Date();
  const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const todayEnd = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
  const weekStart = new Date(now);
  weekStart.setDate(now.getDate() - now.getDay()); // Sunday as start
  weekStart.setHours(0, 0, 0, 0);
  const weekEnd = new Date(weekStart);
  weekEnd.setDate(weekStart.getDate() + 7);
  const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
  const monthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 1);
  const yearStart = new Date(now.getFullYear(), 0, 1);
  const yearEnd = new Date(now.getFullYear() + 1, 0, 1);

  // Calculate totals
  const totalToday = expenses.filter(exp => {
    if (!exp.date) return false;
    const d = parseDate(exp.date);
    return d >= todayStart && d < todayEnd;
  }).reduce((sum, exp) => sum + (exp.amount || 0), 0);

  const totalWeek = expenses.filter(exp => {
    if (!exp.date) return false;
    const d = parseDate(exp.date);
    return d >= weekStart && d < weekEnd;
  }).reduce((sum, exp) => sum + (exp.amount || 0), 0);

  const totalMonth = expenses.filter(exp => {
    if (!exp.date) return false;
    const d = parseDate(exp.date);
    return d >= monthStart && d < monthEnd;
  }).reduce((sum, exp) => sum + (exp.amount || 0), 0);

  const totalYear = expenses.filter(exp => {
    if (!exp.date) return false;
    const d = parseDate(exp.date);
    return d >= yearStart && d < yearEnd;
  }).reduce((sum, exp) => sum + (exp.amount || 0), 0);

  // Ambil data dari localStorage saat mount
  useEffect(() => {
    const stored = localStorage.getItem('expenses');
    if (stored) setExpenses(JSON.parse(stored));
  }, []);



  const handleChange = (e) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!form.name || !form.date || !form.amount) return;
    if (isEditing) {
      const updatedExpenses = expenses.map(exp => exp.id === form.id ? { ...form, amount: Number(form.amount) } : exp);
      setExpenses(updatedExpenses);
      localStorage.setItem('expenses', JSON.stringify(updatedExpenses));
      setIsEditing(false);
    } else {
      const newExpenses = [
        ...expenses,
        {
          ...form,
          id: Date.now(),
          amount: Number(form.amount)
        }
      ];
      setExpenses(newExpenses);
      localStorage.setItem('expenses', JSON.stringify(newExpenses));
    }
    setForm({ id: null, name: '', date: '', amount: '' });
    setShowModal(false);
  };

  const handleEdit = (expense) => {
    setForm(expense);
    setIsEditing(true);
    setShowModal(true);
  };

  const handleAddNew = () => {
    setForm({ id: null, name: '', date: new Date().toISOString().split('T')[0], amount: '' });
    setIsEditing(false);
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setIsEditing(false);
    setForm({ id: null, name: '', date: '', amount: '' });
  };

  const handleDelete = (id) => {
    if (window.confirm('Hapus pengeluaran ini?')) {
      const newExpenses = expenses.filter(exp => exp.id !== id);
      setExpenses(newExpenses);
      localStorage.setItem('expenses', JSON.stringify(newExpenses));
    }
  };

  return (
    <div className="w-full max-w-screen-md mx-auto px-2 sm:px-4 space-y-4" style={{ fontFamily: 'Poppins, Nunito, Inter, sans-serif' }}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <h1 className="text-2xl sm:text-3xl font-bold mb-2 text-center" style={{ letterSpacing: 0.5, background: 'var(--theme-gradient, linear-gradient(90deg, #4caf50, #22d3ee))', WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent', backgroundClip: 'text', color: 'transparent' }}>Pengeluaran</h1>
        <ModernButton
          onClick={handleAddNew}
          color="primary"
          size="large"
          style={{
            borderRadius: 14,
            boxShadow: '0 4px 24px rgba(16,185,129,0.13)',
            fontWeight: 700,
            fontSize: 16,
            padding: '12px 28px',
            minHeight: 48,
            minWidth: 140,
            letterSpacing: 0.5
          }}
          startIcon={<Plus style={{ width: 20, height: 20 }} />}
        >
          Tambah
        </ModernButton>
      </div>

      {/* Period Selector */}
      <div className="flex flex-wrap gap-2 items-center mb-6">
        <label className="font-semibold flex items-center"><Filter size={16} className="inline mr-1" />Periode:</label>
        {periodOptions.map(opt => (
          <button
            key={opt.value}
            className={`px-3 py-1 rounded-full font-medium border transition-colors ${selectedPeriod === opt.value ? 'bg-pink-500 text-white border-pink-500' : 'bg-pink-50 text-pink-700 border-pink-200'}`}
            onClick={() => setSelectedPeriod(opt.value)}
          >
            {opt.label}
          </button>
        ))}
      </div>

      {/* Summary Cards */}
      <div className="w-full mb-4 flex flex-col gap-4 items-center">
        {[
          { value: 'day', label: 'Hari Ini', total: totalToday },
          { value: 'week', label: 'Minggu Ini', total: totalWeek },
          { value: 'month', label: 'Bulan Ini', total: totalMonth },
          { value: 'year', label: 'Tahun Ini', total: totalYear },
        ].map(opt => (
          <div
            key={opt.value}
            className="bg-white w-full py-2 px-4 rounded-2xl shadow border flex items-center justify-center transition-transform duration-150 hover:-translate-y-1 hover:shadow-lg"
          >
            <span className="font-semibold text-base md:text-lg text-pink-500 text-center tracking-tight mr-2">
              {opt.label}
            </span>
            <span className="text-2xl font-semibold text-pink-500 leading-tight">
              {opt.total.toLocaleString('id-ID', { style: 'currency', currency: 'IDR' })}
            </span>
          </div>
        ))}
      </div>

      {/* Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">
                {isEditing ? 'Edit Pengeluaran' : 'Tambah Pengeluaran'}
              </h3>
              <button
                onClick={handleCloseModal}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="h-6 w-6" />
              </button>
            </div>
            <form onSubmit={handleSubmit} className="p-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Nama Pengeluaran
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={form.name}
                    onChange={handleChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 outline-none transition-colors"
                    placeholder="Masukkan nama pengeluaran"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Tanggal
                  </label>
                  <input
                    type="date"
                    name="date"
                    value={form.date}
                    onChange={handleChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 outline-none transition-colors"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Jumlah (Rp)
                  </label>
                  <input
                    type="number"
                    name="amount"
                    value={form.amount}
                    onChange={handleChange}
                    min="0"
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 outline-none transition-colors"
                    placeholder="0"
                  />
                </div>
              </div>
              <div className="flex gap-3 mt-6 pt-4 border-t border-gray-200">
                <ModernButton
                  type="button"
                  color="inherit"
                  onClick={handleCloseModal}
                  style={{
                    flex: 1,
                    borderRadius: 8,
                    fontWeight: 600,
                    padding: '10px 16px'
                  }}
                >
                  Batal
                </ModernButton>
                <ModernButton
                  type="submit"
                  color="primary"
                  style={{
                    flex: 1,
                    borderRadius: 8,
                    fontWeight: 600,
                    padding: '10px 16px'
                  }}
                >
                  {isEditing ? 'Update' : 'Simpan'}
                </ModernButton>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Expense List */}
      <div className="card">
        <div className="card-header bg-light">
          <h5 className="card-title mb-0 fw-semibold text-dark">
            Daftar Pengeluaran ({filteredExpenses.length})
          </h5>
        </div>
        <div className="card-body p-0">
          {filteredExpenses.length > 0 ? (
            <>
              {/* Desktop Table */}
              <div className="d-none d-md-block">
                <div className="table-responsive">
                  <table className="table table-hover mb-0">
                    <thead className="table-light">
                      <tr>
                        <th scope="col" className="fw-semibold text-dark border-0 ps-4">Nama Pengeluaran</th>
                        <th scope="col" className="fw-semibold text-dark border-0">Tanggal</th>
                        <th scope="col" className="fw-semibold text-dark border-0 text-end">Jumlah</th>
                        <th scope="col" className="fw-semibold text-dark border-0 text-center pe-4">Aksi</th>
                      </tr>
                    </thead>
                    <tbody>
                      {filteredExpenses.sort((a, b) => parseDate(b.date) - parseDate(a.date)).map((expense) => (
                        <tr key={expense.id} className="border-light">
                          <td className="ps-4 py-3">
                            <span className="fw-medium text-dark">{expense.name}</span>
                          </td>
                          <td className="py-3">
                            <span className="text-muted">{new Date(expense.date).toLocaleDateString('id-ID', {
                              weekday: 'short',
                              year: 'numeric',
                              month: 'short',
                              day: 'numeric'
                            })}</span>
                          </td>
                          <td className="py-3 text-end">
                            <span className="fw-semibold text-danger">
                              {expense.amount.toLocaleString('id-ID', { style: 'currency', currency: 'IDR' })}
                            </span>
                          </td>
                          <td className="py-3 text-center pe-4">
                            <div className="d-flex align-items-center justify-content-center gap-2">
                              <button
                                onClick={() => handleEdit(expense)}
                                className="btn btn-outline-primary btn-sm d-flex align-items-center justify-content-center"
                                style={{ width: '32px', height: '32px' }}
                                title="Edit"
                              >
                                <Edit2 className="h-4 w-4" />
                              </button>
                              <button
                                onClick={() => handleDelete(expense.id)}
                                className="btn btn-outline-danger btn-sm d-flex align-items-center justify-content-center"
                                style={{ width: '32px', height: '32px' }}
                                title="Hapus"
                              >
                                <Trash2 className="h-4 w-4" />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Mobile Cards */}
              <div className="d-md-none">
                <div className="p-3 bg-light border-bottom">
                  <div className="row text-center">
                    <div className="col-4">
                      <small className="fw-semibold text-muted text-uppercase">Nama</small>
                    </div>
                    <div className="col-3">
                      <small className="fw-semibold text-muted text-uppercase">Tanggal</small>
                    </div>
                    <div className="col-3">
                      <small className="fw-semibold text-muted text-uppercase">Jumlah</small>
                    </div>
                    <div className="col-2">
                      <small className="fw-semibold text-muted text-uppercase">Aksi</small>
                    </div>
                  </div>
                </div>
                {filteredExpenses.sort((a, b) => parseDate(b.date) - parseDate(a.date)).map((expense) => (
                  <div key={expense.id} className="border-bottom border-light">
                    <div className="p-3">
                      <div className="row align-items-center">
                        <div className="col-4">
                          <span className="fw-medium text-dark small text-truncate d-block">{expense.name}</span>
                        </div>
                        <div className="col-3">
                          <span className="text-muted small">{new Date(expense.date).toLocaleDateString('id-ID', {
                            day: '2-digit',
                            month: '2-digit'
                          })}</span>
                        </div>
                        <div className="col-3">
                          <span className="fw-semibold text-danger small">
                            {(expense.amount / 1000).toFixed(0)}k
                          </span>
                        </div>
                        <div className="col-2">
                          <div className="d-flex flex-column gap-1">
                            <button
                              onClick={() => handleEdit(expense)}
                              className="btn btn-outline-primary btn-sm d-flex align-items-center justify-content-center p-1"
                              style={{ width: '24px', height: '24px' }}
                              title="Edit"
                            >
                              <Edit2 style={{ width: '12px', height: '12px' }} />
                            </button>
                            <button
                              onClick={() => handleDelete(expense.id)}
                              className="btn btn-outline-danger btn-sm d-flex align-items-center justify-content-center p-1"
                              style={{ width: '24px', height: '24px' }}
                              title="Hapus"
                            >
                              <Trash2 style={{ width: '12px', height: '12px' }} />
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </>
          ) : (
            <div className="text-center py-5">
              <DollarSign className="mx-auto h-12 w-12 text-gray-400 mb-3" />
              <h5 className="fw-medium text-gray-900 mb-2">Belum ada pengeluaran</h5>
              <p className="text-muted mb-0">
                Mulai dengan menambahkan pengeluaran pertama Anda.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ExpenseList;
