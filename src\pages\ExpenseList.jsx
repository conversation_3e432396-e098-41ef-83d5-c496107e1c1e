import React, { useState, useEffect } from 'react';
import { Plus, Edit2, Trash2, Search, DollarSign, Filter } from 'lucide-react';
import ModernButton from '../components/ModernButton';

const periodOptions = [
  { value: 'day', label: 'Hari Ini' },
  { value: 'week', label: 'Minggu Ini' },
  { value: 'month', label: 'Bulan Ini' },
  { value: 'year', label: 'Tahun Ini' },
];

const ExpenseList = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('month');
  const [expenses, setExpenses] = useState([]);
  const [search, setSearch] = useState('');
  const [form, setForm] = useState({
    id: null,
    name: '',
    date: '',
    amount: ''
  });
  const [isEditing, setIsEditing] = useState(false);

  // Helper: parse date string to Date object (YYYY-MM-DD)
  const parseDate = (dateStr) => {
    if (!dateStr) return null;
    // Accepts YYYY-MM-DD or YYYY/MM/DD
    const [y, m, d] = dateStr.split(/[-\/]/);
    return new Date(Number(y), Number(m) - 1, Number(d));
  };

  // Filter expenses by selected period
  function filterExpensesByPeriod(expenses, period) {
    if (!Array.isArray(expenses)) return [];
    const now = new Date();
    let start, end;
    switch (period) {
      case 'day':
        start = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        end = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
        break;
      case 'week': {
        const day = now.getDay() || 7;
        start = new Date(now);
        start.setDate(now.getDate() - day + 1);
        start.setHours(0, 0, 0, 0);
        end = new Date(start);
        end.setDate(start.getDate() + 7);
        break;
      }
      case 'month':
        start = new Date(now.getFullYear(), now.getMonth(), 1);
        end = new Date(now.getFullYear(), now.getMonth() + 1, 1);
        break;
      case 'year':
        start = new Date(now.getFullYear(), 0, 1);
        end = new Date(now.getFullYear() + 1, 0, 1);
        break;
      default:
        start = null;
        end = null;
    }
    return expenses.filter(exp => {
      if (!exp.date) return false;
      const d = parseDate(exp.date);
      if (start && d < start) return false;
      if (end && d >= end) return false;
      return true;
    });
  }

  const filteredExpenses = filterExpensesByPeriod(expenses, selectedPeriod);

  // Calculate total for filtered expenses
  const totalFiltered = filteredExpenses.reduce((sum, exp) => sum + (exp.amount || 0), 0);

  // Helper: get start/end of today, week, month, year
  const now = new Date();
  const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const todayEnd = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
  const weekStart = new Date(now);
  weekStart.setDate(now.getDate() - now.getDay()); // Sunday as start
  weekStart.setHours(0, 0, 0, 0);
  const weekEnd = new Date(weekStart);
  weekEnd.setDate(weekStart.getDate() + 7);
  const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
  const monthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 1);
  const yearStart = new Date(now.getFullYear(), 0, 1);
  const yearEnd = new Date(now.getFullYear() + 1, 0, 1);

  // Calculate totals
  const totalToday = expenses.filter(exp => {
    if (!exp.date) return false;
    const d = parseDate(exp.date);
    return d >= todayStart && d < todayEnd;
  }).reduce((sum, exp) => sum + (exp.amount || 0), 0);

  const totalWeek = expenses.filter(exp => {
    if (!exp.date) return false;
    const d = parseDate(exp.date);
    return d >= weekStart && d < weekEnd;
  }).reduce((sum, exp) => sum + (exp.amount || 0), 0);

  const totalMonth = expenses.filter(exp => {
    if (!exp.date) return false;
    const d = parseDate(exp.date);
    return d >= monthStart && d < monthEnd;
  }).reduce((sum, exp) => sum + (exp.amount || 0), 0);

  const totalYear = expenses.filter(exp => {
    if (!exp.date) return false;
    const d = parseDate(exp.date);
    return d >= yearStart && d < yearEnd;
  }).reduce((sum, exp) => sum + (exp.amount || 0), 0);

  // Ambil data dari localStorage saat mount
  useEffect(() => {
    const stored = localStorage.getItem('expenses');
    if (stored) setExpenses(JSON.parse(stored));
  }, []);



  const handleChange = (e) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!form.name || !form.date || !form.amount) return;
    if (isEditing) {
      const updatedExpenses = expenses.map(exp => exp.id === form.id ? { ...form, amount: Number(form.amount) } : exp);
      setExpenses(updatedExpenses);
      localStorage.setItem('expenses', JSON.stringify(updatedExpenses));
      setIsEditing(false);
    } else {
      const newExpenses = [
        ...expenses,
        {
          ...form,
          id: Date.now(),
          amount: Number(form.amount)
        }
      ];
      setExpenses(newExpenses);
      localStorage.setItem('expenses', JSON.stringify(newExpenses));
    }
    setForm({ id: null, name: '', date: '', amount: '' });
  };

  const handleEdit = (expense) => {
    setForm(expense);
    setIsEditing(true);
  };

  const handleDelete = (id) => {
    if (window.confirm('Hapus pengeluaran ini?')) {
      const newExpenses = expenses.filter(exp => exp.id !== id);
      setExpenses(newExpenses);
      localStorage.setItem('expenses', JSON.stringify(newExpenses));
    }
  };

  return (
    <div className="w-full max-w-screen-md mx-auto px-2 sm:px-4 space-y-4" style={{ fontFamily: 'Poppins, Nunito, Inter, sans-serif' }}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <h1 className="text-2xl sm:text-3xl font-bold mb-2 text-center" style={{ letterSpacing: 0.5, background: 'var(--theme-gradient, linear-gradient(90deg, #4caf50, #22d3ee))', WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent', backgroundClip: 'text', color: 'transparent' }}>Pengeluaran</h1>
      </div>

      {/* Period Selector */}
      <div className="flex flex-wrap gap-2 items-center mb-6">
        <label className="font-semibold flex items-center"><Filter size={16} className="inline mr-1" />Periode:</label>
        {periodOptions.map(opt => (
          <button
            key={opt.value}
            className={`px-3 py-1 rounded-full font-medium border transition-colors ${selectedPeriod === opt.value ? 'bg-pink-500 text-white border-pink-500' : 'bg-pink-50 text-pink-700 border-pink-200'}`}
            onClick={() => setSelectedPeriod(opt.value)}
          >
            {opt.label}
          </button>
        ))}
      </div>

      {/* Summary Cards */}
      <div className="w-full mb-4 flex flex-col gap-4 items-center">
        {[
          { value: 'day', label: 'Hari Ini', total: totalToday },
          { value: 'week', label: 'Minggu Ini', total: totalWeek },
          { value: 'month', label: 'Bulan Ini', total: totalMonth },
          { value: 'year', label: 'Tahun Ini', total: totalYear },
        ].map(opt => (
          <div
            key={opt.value}
            className="bg-white w-full py-2 px-4 rounded-2xl shadow border flex items-center justify-center transition-transform duration-150 hover:-translate-y-1 hover:shadow-lg"
          >
            <span className="font-semibold text-base md:text-lg text-pink-500 text-center tracking-tight mr-2">
              {opt.label}
            </span>
            <span className="text-2xl font-semibold text-pink-500 leading-tight">
              {opt.total.toLocaleString('id-ID', { style: 'currency', currency: 'IDR' })}
            </span>
          </div>
        ))}
      </div>

      {/* Form Card */}
      <div className="bg-white shadow rounded-xl border border-gray-100 px-2 py-3 sm:px-8 sm:py-6 mt-6 mb-8">
        <form onSubmit={handleSubmit} className="flex flex-col gap-4 w-full">
          <div className="flex flex-col gap-2 w-full">
            <div className="relative w-full">
              <input
                type="text"
                name="name"
                value={form.name}
                onChange={handleChange}
                required
                className="px-3 py-2 rounded-lg border border-gray-200 bg-gray-50 text-base font-medium outline-none transition focus:ring-2 focus:ring-emerald-200 mb-1"
                placeholder="Nama Pengeluaran"
              />
            </div>
            <div className="responsive-input-group flex flex-col gap-2 w-full">
              <input
                type="date"
                name="date"
                value={form.date}
                onChange={handleChange}
                required
                className="px-3 py-2 rounded-lg border border-gray-200 bg-gray-50 text-base font-medium outline-none transition focus:ring-2 focus:ring-emerald-200"
                placeholder="Tanggal"
              />
              <input
                type="number"
                name="amount"
                value={form.amount}
                onChange={handleChange}
                min="0"
                required
                className="px-3 py-2 rounded-lg border border-gray-200 bg-gray-50 text-base font-medium outline-none transition focus:ring-2 focus:ring-emerald-200"
                placeholder="Harga (Rp)"
              />
            </div>
          </div>
          <div className="flex flex-col sm:flex-row gap-2 mt-2 w-full">
            <ModernButton
  type="submit"
  color="primary"
  size="large"
  style={{
    borderRadius: 14,
    boxShadow: '0 4px 24px rgba(16,185,129,0.13)',
    fontWeight: 700,
    fontSize: 16,
    padding: '12px 28px',
    minHeight: 48,
    minWidth: 140,
    letterSpacing: 0.5
  }}
  startIcon={<Plus style={{ width: 20, height: 20 }} />}
>
  {isEditing ? 'Update' : 'Tambah'}
</ModernButton>
            {isEditing && (
              <ModernButton
  type="button"
  color="inherit"
  size="large"
  style={{
    borderRadius: 14,
    boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
    fontWeight: 600,
    fontSize: 16,
    padding: '12px 28px',
    minHeight: 48,
    minWidth: 120
  }}
  onClick={() => { setIsEditing(false); setForm({ id: null, name: '', date: '', amount: '' }); }}
>
  Batal
</ModernButton>
            )}
          </div>
        </form>
      </div>

      {/* Expense List */}
      <div className="card">
        {filteredExpenses.length > 0 ? (
          <>
            {/* Desktop List */}
            <div className="d-none d-md-block">
              {filteredExpenses.sort((a, b) => parseDate(b.date) - parseDate(a.date)).map((expense) => (
                <div key={expense.id} className="card mb-2 border-light">
                  <div className="card-body p-3">
                    <div className="d-flex align-items-center justify-content-between">
                      <div className="d-flex align-items-center gap-3">
                        <div className="d-flex flex-column">
                          <span className="fw-medium small">{expense.name}</span>
                          <span className="text-muted" style={{ fontSize: '0.75rem' }}>{expense.date}</span>
                        </div>
                        <span className="fw-semibold small text-danger">{expense.amount.toLocaleString('id-ID', { style: 'currency', currency: 'IDR' })}</span>
                      </div>
                      <div className="d-flex align-items-center gap-2">
                        <button
                          onClick={() => handleEdit(expense)}
                          className="btn btn-outline-success btn-sm d-flex align-items-center justify-content-center"
                          style={{ width: '32px', height: '32px' }}
                          title="Edit"
                        >
                          <Edit2 className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDelete(expense.id)}
                          className="btn btn-outline-danger btn-sm d-flex align-items-center justify-content-center"
                          style={{ width: '32px', height: '32px' }}
                          title="Hapus"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Mobile List */}
            <div className="d-md-none">
              {filteredExpenses.sort((a, b) => parseDate(b.date) - parseDate(a.date)).map((expense) => (
                <div key={expense.id} className="card mb-2 border-light">
                  <div className="card-body p-2" style={{ minHeight: 48 }}>
                    <div className="d-flex align-items-center justify-content-between gap-3">
                      <div className="flex-grow-1 d-flex align-items-center gap-3 overflow-hidden">
                        <span className="text-truncate fw-medium small">{expense.name}</span>
                        <span className="text-muted text-nowrap" style={{ fontSize: '0.75rem' }}>{expense.date}</span>
                        <span className="fw-semibold small text-danger text-nowrap">{expense.amount.toLocaleString('id-ID', { style: 'currency', currency: 'IDR' })}</span>
                      </div>
                      <div className="d-flex align-items-center gap-1 flex-shrink-0">
                        <button
                          onClick={() => handleEdit(expense)}
                          className="btn btn-outline-success btn-sm d-flex align-items-center justify-content-center"
                          style={{ width: '28px', height: '28px' }}
                          title="Edit"
                        >
                          <Edit2 className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDelete(expense.id)}
                          className="btn btn-outline-danger btn-sm d-flex align-items-center justify-content-center"
                          style={{ width: '28px', height: '28px' }}
                          title="Hapus"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </>
        ) : (
          <div className="text-center py-12">
            <DollarSign className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Belum ada pengeluaran</h3>
            <p className="mt-1 text-sm text-gray-500">
              Mulai dengan menambahkan pengeluaran pertama Anda.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ExpenseList;
