class HPPService {
  constructor() {
    this.storageKey = 'hpp_records';
    this.records = this.loadRecords();
  }

  loadRecords() {
    const savedRecords = localStorage.getItem(this.storageKey);
    return savedRecords ? JSON.parse(savedRecords) : [];
  }

  saveRecords() {
    localStorage.setItem(this.storageKey, JSON.stringify(this.records));
  }

  getAllRecords() {
    return [...this.records];
  }

  getRecordById(id) {
    return this.records.find(record => record.id === id);
  }

  addRecord(record) {
    const newRecord = {
      id: Date.now(),
      productName: record.productName,
      ingredients: record.ingredients,
      laborCost: record.laborCost,
      utilitiesCost: record.utilitiesCost,
      packagingCost: record.packagingCost,
      otherCosts: record.otherCosts,
      hpp: record.hpp,
      createdAt: new Date().toISOString()
    };
    this.records.push(newRecord);
    this.saveRecords();
    return newRecord;
  }

  updateRecord(id, updates) {
    const index = this.records.findIndex(record => record.id === id);
    if (index === -1) return null;
    
    this.records[index] = {
      ...this.records[index],
      ...updates,
      updatedAt: new Date().toISOString()
    };
    this.saveRecords();
    return this.records[index];
  }

  deleteRecord(id) {
    const index = this.records.findIndex(record => record.id === id);
    if (index === -1) return false;
    
    this.records.splice(index, 1);
    this.saveRecords();
    return true;
  }

  searchRecords(query = '') {
    const searchLower = query.toLowerCase();
    return this.records.filter(record => 
      record.productName.toLowerCase().includes(searchLower)
    );
  }
}

// Export a singleton instance
export const hppService = new HPPService();
