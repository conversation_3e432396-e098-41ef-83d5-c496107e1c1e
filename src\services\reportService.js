import { getInvoices, getCustomers } from './storage'
import { startOfDay, endOfDay, startOfWeek, endOfWeek, startOfMonth, endOfMonth, startOfYear, endOfYear, format, parseISO, isWithinInterval, subDays, subWeeks, subMonths, subYears } from 'date-fns'
import { id } from 'date-fns/locale'
import { hppService } from './hppService'

// Get revenue data by period
export const getRevenueByPeriod = (period = 'month', startDate = null, endDate = null) => {
  const invoices = getInvoices().filter(invoice => invoice.status === 'paid')
  const customers = getCustomers()
  
  let filteredInvoices = invoices
  
  if (startDate && endDate) {
    // Custom date range
    const start = startOfDay(parseISO(startDate))
    const end = endOfDay(parseISO(endDate))
    
    filteredInvoices = invoices.filter(invoice => {
      const invoiceDate = parseISO(invoice.tanggal)
      return isWithinInterval(invoiceDate, { start, end })
    })
  } else {
    // Predefined periods
    const now = new Date()
    let start, end
    
    switch (period) {
      case 'day':
        start = startOfDay(now)
        end = endOfDay(now)
        break
      case 'week':
        start = startOfWeek(now, { locale: id })
        end = endOfWeek(now, { locale: id })
        break
      case 'month':
        start = startOfMonth(now)
        end = endOfMonth(now)
        break
      case 'year':
        start = startOfYear(now)
        end = endOfYear(now)
        break
      default:
        start = startOfMonth(now)
        end = endOfMonth(now)
    }
    
    filteredInvoices = invoices.filter(invoice => {
      const invoiceDate = parseISO(invoice.tanggal)
      return isWithinInterval(invoiceDate, { start, end })
    })
  }
  
  // Calculate total revenue
  const totalRevenue = filteredInvoices.reduce((sum, invoice) => sum + invoice.total, 0)
  
  // Calculate average per invoice
  const averagePerInvoice = filteredInvoices.length > 0 ? totalRevenue / filteredInvoices.length : 0
  
  // Get customer names
  const invoicesWithCustomers = filteredInvoices.map(invoice => ({
    ...invoice,
    customerName: customers.find(c => c.id === invoice.pelangganId)?.nama || 'Unknown'
  }))
  
  return {
    totalRevenue,
    totalInvoices: filteredInvoices.length,
    averagePerInvoice,
    invoices: invoicesWithCustomers,
    period
  }
}

// Get revenue trend data for charts
export const getRevenueTrend = (period = 'month', count = 12) => {
  const invoices = getInvoices().filter(invoice => invoice.status === 'paid')
  const now = new Date()
  const data = []
  
  for (let i = count - 1; i >= 0; i--) {
    let start, end, label
    
    switch (period) {
      case 'day':
        const day = subDays(now, i)
        start = startOfDay(day)
        end = endOfDay(day)
        label = format(day, 'dd MMM', { locale: id })
        break
      case 'week':
        const week = subWeeks(now, i)
        start = startOfWeek(week, { locale: id })
        end = endOfWeek(week, { locale: id })
        label = `Minggu ${format(start, 'dd MMM', { locale: id })}`
        break
      case 'month':
        const month = subMonths(now, i)
        start = startOfMonth(month)
        end = endOfMonth(month)
        label = format(month, 'MMM yyyy', { locale: id })
        break
      case 'year':
        const year = subYears(now, i)
        start = startOfYear(year)
        end = endOfYear(year)
        label = format(year, 'yyyy')
        break
      default:
        const defaultMonth = subMonths(now, i)
        start = startOfMonth(defaultMonth)
        end = endOfMonth(defaultMonth)
        label = format(defaultMonth, 'MMM yyyy', { locale: id })
    }
    
    const periodInvoices = invoices.filter(invoice => {
      const invoiceDate = parseISO(invoice.tanggal)
      return isWithinInterval(invoiceDate, { start, end })
    })
    
    const revenue = periodInvoices.reduce((sum, invoice) => sum + invoice.total, 0)
    
    data.push({
      period: label,
      revenue,
      invoiceCount: periodInvoices.length,
      date: format(start, 'yyyy-MM-dd')
    })
  }
  
  return data
}

// Get top customers by revenue
export const getTopCustomers = (period = 'month', limit = 5) => {
  const { invoices } = getRevenueByPeriod(period)
  const customers = getCustomers()
  
  // Group by customer
  const customerRevenue = {}
  
  invoices.forEach(invoice => {
    const customerId = invoice.pelangganId
    if (!customerRevenue[customerId]) {
      customerRevenue[customerId] = {
        customerId,
        customerName: customers.find(c => c.id === customerId)?.nama || 'Unknown',
        totalRevenue: 0,
        invoiceCount: 0
      }
    }
    customerRevenue[customerId].totalRevenue += invoice.total
    customerRevenue[customerId].invoiceCount += 1
  })
  
  // Sort by revenue and limit
  return Object.values(customerRevenue)
    .sort((a, b) => b.totalRevenue - a.totalRevenue)
    .slice(0, limit)
}

// Get revenue summary statistics
export const getRevenueSummary = () => {
  const invoices = getInvoices().filter(invoice => invoice.status === 'paid')
  const now = new Date()
  
  // Today
  const todayRevenue = getRevenueByPeriod('day').totalRevenue
  
  // This week
  const weekRevenue = getRevenueByPeriod('week').totalRevenue
  
  // This month
  const monthRevenue = getRevenueByPeriod('month').totalRevenue
  
  // This year
  const yearRevenue = getRevenueByPeriod('year').totalRevenue
  
  // Previous month for comparison
  const lastMonth = subMonths(now, 1)
  const lastMonthStart = format(startOfMonth(lastMonth), 'yyyy-MM-dd')
  const lastMonthEnd = format(endOfMonth(lastMonth), 'yyyy-MM-dd')
  const lastMonthRevenue = getRevenueByPeriod('month', lastMonthStart, lastMonthEnd).totalRevenue
  
  // Calculate growth
  const monthGrowth = lastMonthRevenue > 0 
    ? ((monthRevenue - lastMonthRevenue) / lastMonthRevenue) * 100 
    : 0
  
  return {
    today: todayRevenue,
    week: weekRevenue,
    month: monthRevenue,
    year: yearRevenue,
    lastMonth: lastMonthRevenue,
    monthGrowth: monthGrowth
  }
}

// --- EXPENSE AGGREGATION FUNCTIONS ---

// Get expense (pengeluaran) data by period
export const getExpenseByPeriod = (period = 'month', startDate = null, endDate = null) => {
  const records = hppService.getAllRecords();
  let filteredRecords = records;

  if (startDate && endDate) {
    const start = startOfDay(parseISO(startDate));
    const end = endOfDay(parseISO(endDate));
    filteredRecords = records.filter(record => {
      const recordDate = parseISO(record.createdAt);
      return isWithinInterval(recordDate, { start, end });
    });
  } else {
    const now = new Date();
    let start, end;
    switch (period) {
      case 'day':
        start = startOfDay(now); end = endOfDay(now); break;
      case 'week':
        start = startOfWeek(now, { locale: id }); end = endOfWeek(now, { locale: id }); break;
      case 'month':
        start = startOfMonth(now); end = endOfMonth(now); break;
      case 'year':
        start = startOfYear(now); end = endOfYear(now); break;
      default:
        start = startOfMonth(now); end = endOfMonth(now);
    }
    filteredRecords = records.filter(record => {
      const recordDate = parseISO(record.createdAt);
      return isWithinInterval(recordDate, { start, end });
    });
  }

  // Calculate total expenses (sum of all cost fields)
  const totalExpense = filteredRecords.reduce((sum, record) => {
    return sum +
      (Number(record.laborCost) || 0) +
      (Number(record.utilitiesCost) || 0) +
      (Number(record.packagingCost) || 0) +
      (Number(record.otherCosts) || 0);
  }, 0);

  // Average per record
  const averagePerRecord = filteredRecords.length > 0 ? totalExpense / filteredRecords.length : 0;

  return {
    totalExpense,
    totalRecords: filteredRecords.length,
    averagePerRecord,
    records: filteredRecords,
    period
  };
};

// Get expense trend data for charts
export const getExpenseTrend = (period = 'month', count = 12) => {
  const records = hppService.getAllRecords();
  const now = new Date();
  const data = [];

  for (let i = count - 1; i >= 0; i--) {
    let start, end, label;
    switch (period) {
      case 'day': {
        const day = subDays(now, i);
        start = startOfDay(day); end = endOfDay(day);
        label = format(day, 'dd MMM', { locale: id });
        break;
      }
      case 'week': {
        const week = subWeeks(now, i);
        start = startOfWeek(week, { locale: id }); end = endOfWeek(week, { locale: id });
        label = `Minggu ${format(start, 'dd MMM', { locale: id })}`;
        break;
      }
      case 'month': {
        const month = subMonths(now, i);
        start = startOfMonth(month); end = endOfMonth(month);
        label = format(month, 'MMM yyyy', { locale: id });
        break;
      }
      case 'year': {
        const year = subYears(now, i);
        start = startOfYear(year); end = endOfYear(year);
        label = format(year, 'yyyy');
        break;
      }
      default: {
        const defaultMonth = subMonths(now, i);
        start = startOfMonth(defaultMonth); end = endOfMonth(defaultMonth);
        label = format(defaultMonth, 'MMM yyyy', { locale: id });
      }
    }
    const periodRecords = records.filter(record => {
      const recordDate = parseISO(record.createdAt);
      return isWithinInterval(recordDate, { start, end });
    });
    const expense = periodRecords.reduce((sum, record) => {
      return sum +
        (Number(record.laborCost) || 0) +
        (Number(record.utilitiesCost) || 0) +
        (Number(record.packagingCost) || 0) +
        (Number(record.otherCosts) || 0);
    }, 0);
    data.push({
      period: label,
      expense,
      recordCount: periodRecords.length,
      date: format(start, 'yyyy-MM-dd')
    });
  }
  return data;
};

// Get expense summary statistics
export const getExpenseSummary = () => {
  const now = new Date();
  // Today
  const todayExpense = getExpenseByPeriod('day').totalExpense;
  // This week
  const weekExpense = getExpenseByPeriod('week').totalExpense;
  // This month
  const monthExpense = getExpenseByPeriod('month').totalExpense;
  // This year
  const yearExpense = getExpenseByPeriod('year').totalExpense;
  // Previous month for comparison
  const lastMonth = subMonths(now, 1);
  const lastMonthStart = format(startOfMonth(lastMonth), 'yyyy-MM-dd');
  const lastMonthEnd = format(endOfMonth(lastMonth), 'yyyy-MM-dd');
  const lastMonthExpense = getExpenseByPeriod('month', lastMonthStart, lastMonthEnd).totalExpense;
  // Calculate growth
  const monthGrowth = lastMonthExpense > 0 
    ? ((monthExpense - lastMonthExpense) / lastMonthExpense) * 100 
    : 0;
  return {
    today: todayExpense,
    week: weekExpense,
    month: monthExpense,
    year: yearExpense,
    lastMonth: lastMonthExpense,
    monthGrowth: monthGrowth
  };
};

// Export data for external use
export const exportRevenueData = (period = 'month') => {
  const data = getRevenueByPeriod(period)
  const trend = getRevenueTrend(period)
  const topCustomers = getTopCustomers(period)
  
  return {
    summary: data,
    trend,
    topCustomers,
    exportDate: format(new Date(), 'yyyy-MM-dd HH:mm:ss', { locale: id })
  }
}
