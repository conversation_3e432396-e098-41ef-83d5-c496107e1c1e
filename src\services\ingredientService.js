class IngredientService {
  constructor() {
    this.storageKey = 'ingredients';
    this.ingredients = this.loadIngredients();
  }

  loadIngredients() {
    const savedIngredients = localStorage.getItem(this.storageKey);
    return savedIngredients ? JSON.parse(savedIngredients) : [];
  }

  saveIngredients() {
    localStorage.setItem(this.storageKey, JSON.stringify(this.ingredients));
  }

  getAllIngredients() {
    return [...this.ingredients];
  }

  getIngredientById(id) {
    return this.ingredients.find(ingredient => ingredient.id === id);
  }

  addIngredient(ingredient) {
    const newIngredient = {
      id: Date.now(),
      nama: ingredient.nama,
      berat: ingredient.berat,
      harga: ingredient.harga,
      satuan: ingredient.satuan,
      createdAt: new Date().toISOString()
    };
    this.ingredients.push(newIngredient);
    this.saveIngredients();
    return newIngredient;
  }

  updateIngredient(id, updates) {
    const index = this.ingredients.findIndex(ingredient => ingredient.id === id);
    if (index === -1) return null;
    
    this.ingredients[index] = {
      ...this.ingredients[index],
      ...updates,
      updatedAt: new Date().toISOString()
    };
    this.saveIngredients();
    return this.ingredients[index];
  }

  deleteIngredient(id) {
    const index = this.ingredients.findIndex(ingredient => ingredient.id === id);
    if (index === -1) return false;
    
    this.ingredients.splice(index, 1);
    this.saveIngredients();
    return true;
  }

  // Helper method to get ingredients with search filter
  searchIngredients(query = '') {
    const searchLower = query.toLowerCase();
    return this.ingredients.filter(ingredient => 
      ingredient.nama.toLowerCase().includes(searchLower) ||
      ingredient.satuan.toLowerCase().includes(searchLower)
    );
  }

  // Helper to calculate price per unit
  calculatePricePerUnit(ingredient) {
    if (!ingredient.berat || !ingredient.harga) return 0;
    return ingredient.harga / ingredient.berat;
  }
}

// Export a singleton instance
export const ingredientService = new IngredientService();
