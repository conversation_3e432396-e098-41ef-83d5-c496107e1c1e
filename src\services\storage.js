// Storage service untuk mengelola data di localStorage

const STORAGE_KEYS = {
  PRODUCTS: 'roti_ragil_products',
  CUSTOMERS: 'roti_ragil_customers',
  INVOICES: 'roti_ragil_invoices',
  SETTINGS: 'roti_ragil_settings',
  DATA_VERSION: 'roti_ragil_data_version'
}

// Current version of default data
const CURRENT_DATA_VERSION = '2.2.0'

// Generic storage functions
const getFromStorage = (key) => {
  try {
    const data = localStorage.getItem(key)
    return data ? JSON.parse(data) : []
  } catch (error) {
    console.error('Error reading from storage:', error)
    return []
  }
}

const saveToStorage = (key, data) => {
  try {
    localStorage.setItem(key, JSON.stringify(data))
    return true
  } catch (error) {
    console.error('Error saving to storage:', error)
    return false
  }
}

// Data version functions
const getDataVersion = () => {
  try {
    return localStorage.getItem(STORAGE_KEYS.DATA_VERSION) || '1.0.0'
  } catch (error) {
    console.error('Error reading data version:', error)
    return '1.0.0'
  }
}

const setDataVersion = (version) => {
  try {
    localStorage.setItem(STORAGE_KEYS.DATA_VERSION, version)
    return true
  } catch (error) {
    console.error('Error saving data version:', error)
    return false
  }
}

// Product functions
export const getProducts = () => {
  return getFromStorage(STORAGE_KEYS.PRODUCTS)
}

export const saveProduct = (product) => {
  const products = getProducts()
  const newProduct = {
    id: Date.now().toString(),
    ...product,
    createdAt: new Date().toISOString()
  }
  products.push(newProduct)
  saveToStorage(STORAGE_KEYS.PRODUCTS, products)
  return newProduct
}

export const updateProduct = (id, updatedProduct) => {
  const products = getProducts()
  const index = products.findIndex(p => p.id === id)
  if (index !== -1) {
    products[index] = { ...products[index], ...updatedProduct }
    saveToStorage(STORAGE_KEYS.PRODUCTS, products)
    return products[index]
  }
  return null
}

export const deleteProduct = (id) => {
  const products = getProducts()
  const filteredProducts = products.filter(p => p.id !== id)
  saveToStorage(STORAGE_KEYS.PRODUCTS, filteredProducts)
  return true
}

// Customer functions
export const getCustomers = () => {
  return getFromStorage(STORAGE_KEYS.CUSTOMERS)
}

export const saveCustomer = (customer) => {
  const customers = getCustomers()
  const newCustomer = {
    id: Date.now().toString(),
    ...customer,
    createdAt: new Date().toISOString()
  }
  customers.push(newCustomer)
  saveToStorage(STORAGE_KEYS.CUSTOMERS, customers)
  return newCustomer
}

export const updateCustomer = (id, updatedCustomer) => {
  const customers = getCustomers()
  const index = customers.findIndex(c => c.id === id)
  if (index !== -1) {
    customers[index] = { ...customers[index], ...updatedCustomer }
    saveToStorage(STORAGE_KEYS.CUSTOMERS, customers)
    return customers[index]
  }
  return null
}

export const deleteCustomer = (id) => {
  const customers = getCustomers()
  const filteredCustomers = customers.filter(c => c.id !== id)
  saveToStorage(STORAGE_KEYS.CUSTOMERS, filteredCustomers)
  return true
}

// Invoice functions
export const getInvoices = () => {
  return getFromStorage(STORAGE_KEYS.INVOICES)
}

export const saveInvoice = (invoice) => {
  const invoices = getInvoices()
  const newInvoice = {
    id: Date.now().toString(),
    nomorInvoice: generateInvoiceNumber(),
    ...invoice,
    createdAt: new Date().toISOString()
  }
  invoices.push(newInvoice)
  saveToStorage(STORAGE_KEYS.INVOICES, invoices)
  return newInvoice
}

export const updateInvoice = (id, updatedInvoice) => {
  const invoices = getInvoices()
  const index = invoices.findIndex(i => i.id === id)
  if (index !== -1) {
    const currentInvoice = invoices[index]

    // Track when status changes to 'paid'
    if (updatedInvoice.status === 'paid' && currentInvoice.status !== 'paid') {
      updatedInvoice.paidAt = new Date().toISOString()
    }

    invoices[index] = { ...currentInvoice, ...updatedInvoice }
    saveToStorage(STORAGE_KEYS.INVOICES, invoices)
    return invoices[index]
  }
  return null
}

export const deleteInvoice = (id) => {
  const invoices = getInvoices()
  const filteredInvoices = invoices.filter(i => i.id !== id)
  saveToStorage(STORAGE_KEYS.INVOICES, filteredInvoices)
  return true
}

export const getInvoiceById = (id) => {
  const invoices = getInvoices()
  return invoices.find(i => i.id === id)
}

// Helper functions
const generateInvoiceNumber = () => {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const invoices = getInvoices()
  const invoiceCount = invoices.length + 1
  return `INV/${year}${month}/${String(invoiceCount).padStart(4, '0')}`
}

// Default data with consistent IDs for versioning
const getDefaultProducts = () => [
  { id: 'default_001', nama: 'Roti Sosis', harga: 3000, isDefault: true },
  { id: 'default_002', nama: 'Roti Sosis', harga: 2500, isDefault: true },
  { id: 'default_003', nama: 'Roti Meses', harga: 2500, isDefault: true },
  { id: 'default_004', nama: 'Roti Abon', harga: 2500, isDefault: true },
  { id: 'default_005', nama: 'Roti Keju', harga: 2500, isDefault: true },
  { id: 'default_006', nama: 'Roti Kacang Hijau', harga: 2500, isDefault: true },
  { id: 'default_007', nama: 'Roti Kacang Merah', harga: 2500, isDefault: true },
  { id: 'default_008', nama: 'Flossroll', harga: 3500, isDefault: true },
  { id: 'default_009', nama: 'Bolu Gulung', harga: 35000, isDefault: true },
  { id: 'default_010', nama: 'Bolu Gulung', harga: 30000, isDefault: true },
  { id: 'default_011', nama: 'Chiffon Keju', harga: 35000, isDefault: true },
  { id: 'default_012', nama: 'Chiffon Keju', harga: 30000, isDefault: true },
  { id: 'default_013', nama: 'Flossroll Box', harga: 30000, isDefault: true },
  { id: 'default_014', nama: 'Roti Pisang Cokelat', harga: 3000, isDefault: true },
  { id: 'default_015', nama: 'Roti Pisang Keju', harga: 3000, isDefault: true }
]

const getDefaultCustomers = () => [
  { id: 'customer_001', nama: 'Bu Lisferi', alamat: '-', telepon: '-', email: '', isDefault: true },
  { id: 'customer_002', nama: 'Dapur Lezzati', alamat: '-', telepon: '-', email: '', isDefault: true },
  { id: 'customer_003', nama: 'Maruf', alamat: '-', telepon: '-', email: '', isDefault: true }
]

// Initialize with sample data if empty
export const initializeSampleData = () => {
  if (getProducts().length === 0) {
    const defaultProducts = getDefaultProducts()
    defaultProducts.forEach(product => {
      const newProduct = {
        ...product,
        createdAt: new Date().toISOString()
      }
      const products = getProducts()
      products.push(newProduct)
      saveToStorage(STORAGE_KEYS.PRODUCTS, products)
    })
  }

  if (getCustomers().length === 0) {
    const defaultCustomers = getDefaultCustomers()
    defaultCustomers.forEach(customer => {
      const newCustomer = {
        ...customer,
        createdAt: new Date().toISOString()
      }
      const customers = getCustomers()
      customers.push(newCustomer)
      saveToStorage(STORAGE_KEYS.CUSTOMERS, customers)
    })
  }

  // Set initial data version
  setDataVersion(CURRENT_DATA_VERSION)
}

// Smart sync function to add new default data without removing existing data
export const syncDefaultData = () => {
  const currentVersion = getDataVersion()

  console.log(`Current data version: ${currentVersion}, Latest version: ${CURRENT_DATA_VERSION}`)

  if (currentVersion === CURRENT_DATA_VERSION) {
    console.log('Data is already up to date')
    return { updated: false, message: 'Data sudah up to date' }
  }

  let updatedProducts = 0
  let updatedCustomers = 0

  // Sync products
  const existingProducts = getProducts()
  const defaultProducts = getDefaultProducts()
  const existingProductIds = existingProducts.map(p => p.id)

  defaultProducts.forEach(defaultProduct => {
    if (!existingProductIds.includes(defaultProduct.id)) {
      const newProduct = {
        ...defaultProduct,
        createdAt: new Date().toISOString()
      }
      existingProducts.push(newProduct)
      updatedProducts++
    }
  })

  if (updatedProducts > 0) {
    saveToStorage(STORAGE_KEYS.PRODUCTS, existingProducts)
  }

  // Sync customers
  const existingCustomers = getCustomers()
  const defaultCustomers = getDefaultCustomers()
  const existingCustomerIds = existingCustomers.map(c => c.id)

  defaultCustomers.forEach(defaultCustomer => {
    if (!existingCustomerIds.includes(defaultCustomer.id)) {
      const newCustomer = {
        ...defaultCustomer,
        createdAt: new Date().toISOString()
      }
      existingCustomers.push(newCustomer)
      updatedCustomers++
    }
  })

  if (updatedCustomers > 0) {
    saveToStorage(STORAGE_KEYS.CUSTOMERS, existingCustomers)
  }

  // Update data version
  setDataVersion(CURRENT_DATA_VERSION)

  const message = `Berhasil menambahkan ${updatedProducts} produk baru dan ${updatedCustomers} pelanggan baru`
  console.log(message)

  return {
    updated: updatedProducts > 0 || updatedCustomers > 0,
    message,
    updatedProducts,
    updatedCustomers
  }
}

// Auto-sync function to be called on app startup
export const autoSyncDefaultData = () => {
  try {
    // Check if this is first time (no data at all)
    const hasProducts = getProducts().length > 0
    const hasCustomers = getCustomers().length > 0

    if (!hasProducts || !hasCustomers) {
      // First time initialization
      console.log('First time initialization...')
      initializeSampleData()
      return {
        isFirstTime: true,
        message: 'Aplikasi diinisialisasi dengan data default'
      }
    } else {
      // Existing installation, check for updates
      console.log('Checking for data updates...')
      return syncDefaultData()
    }
  } catch (error) {
    console.error('Error in auto-sync:', error)
    return {
      updated: false,
      error: true,
      message: 'Terjadi kesalahan saat sinkronisasi data'
    }
  }
}

// Reset all data and reinitialize with default data
export const resetToDefaultData = () => {
  // Clear existing data
  localStorage.removeItem(STORAGE_KEYS.PRODUCTS)
  localStorage.removeItem(STORAGE_KEYS.CUSTOMERS)
  localStorage.removeItem(STORAGE_KEYS.INVOICES)
  localStorage.removeItem(STORAGE_KEYS.DATA_VERSION)

  // Reinitialize with default data
  initializeSampleData()
}


