import React from 'react';
import {
  X,
  Plus,
  Package,
  Trash2,
} from 'lucide-react';
import { formatRupiah } from '../utils/formatting';
import { ingredientService } from '../services/ingredientService';
import ModernButton from '../components/ModernButton';


const HPPModal = ({
  isOpen,
  onClose,
  formData,
  setFormData,
  ingredients,
  saveHPP,
  editingRecord,
}) => {
  const [units] = React.useState([
    { value: 'g', label: 'Gram' },
    { value: 'kg', label: 'Kilogram' },
    { value: 'pcs', label: 'Pcs' },
  ]);

  const addIngredient = () => {
    setFormData((prev) => ({
      ...prev,
      ingredients: [...prev.ingredients, { ingredientId: null, quantity: '', unit: 'g', harga: 0 }],
    }));
  };

  const removeIngredient = (index) => {
    setFormData((prev) => ({
      ...prev,
      ingredients: prev.ingredients.filter((_, i) => i !== index),
    }));
  };

  const handleInputChange = (e, index = null) => {
    const { name, value } = e.target;
    if (index !== null) {
      if (name === 'ingredientId' && value) {
        const selectedIngredient = ingredients.find(i => i.id === parseInt(value));
        if (selectedIngredient) {
          // Initialize harga to 0 when ingredient is selected
          setFormData((prev) => ({
            ...prev,
            ingredients: prev.ingredients.map((ingredient, i) =>
              i === index ? {
                ...ingredient,
                ingredientId: parseInt(value),
                unit: selectedIngredient.satuan,
                harga: 0
              } : ingredient
            )
          }));
        }
      } else if (name === 'quantity') {
        setFormData((prev) => ({
          ...prev,
          ingredients: prev.ingredients.map((ingredient, i) => {
            if (i === index && ingredient.ingredientId && value !== '') {
              const newQuantity = parseFloat(value) || 0;
              const selectedIngredient = ingredients.find(i => i.id === ingredient.ingredientId);
              if (selectedIngredient) {
                const harga = calculateIngredientHarga(selectedIngredient, newQuantity);
                return { ...ingredient, quantity: value, harga };
              }
            }
            return i === index ? { ...ingredient, quantity: value } : ingredient;
          })
        }));
      } else {
        setFormData((prev) => ({
          ...prev,
          ingredients: prev.ingredients.map((ingredient, i) =>
            i === index ? { ...ingredient, [name]: value } : ingredient
          )
        }));
      }
    } else {
      setFormData((prev) => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  const calculateIngredientHarga = (ingredient, quantity) => {
    const berat = parseFloat(ingredient.berat);
    const hargaTotal = parseFloat(ingredient.harga);
    const quantityNum = parseFloat(quantity) || 0;

    const beratGram = ingredient.satuan === 'kg' ? berat * 1000 : berat;
    const jumlahGram = quantityNum * (ingredient.satuan === 'g' ? 1 : 1000);

    return (jumlahGram / beratGram) * hargaTotal;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md md:max-w-2xl h-[90vh] md:h-auto overflow-hidden">
        <div className="border-b p-4 flex justify-between items-center">
          <h2 className="text-xl font-semibold">
            {editingRecord ? 'Edit HPP' : 'Tambah HPP'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="p-6 overflow-y-auto h-[calc(100vh-16rem)] md:h-auto md:max-h-[60vh]">
          <div className="mb-6">
            <h2 className="text-xl font-semibold mb-4">Informasi Produk</h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Nama Produk
                </label>
                <input
                  type="text"
                  name="productName"
                  value={formData.productName}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
          </div>

          <div className="mb-6">
            <h2 className="text-xl font-semibold mb-4">Bahan Baku</h2>
            {formData.ingredients.map((ingredient, index) => (
              <div key={index} className="mb-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Bahan Baku
                    </label>
                    <select
                      name="ingredientId"
                      value={ingredient.ingredientId || ''}
                      onChange={(e) => {
                        const selectedId = e.target.value;
                        const selectedIngredient = ingredients.find(i => i.id === parseInt(selectedId));
                        if (selectedIngredient) {
                          handleInputChange({
                            target: {
                              name: 'unit',
                              value: selectedIngredient.satuan
                            }
                          }, index);
                        }
                        handleInputChange(e, index);
                      }}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="">Pilih bahan baku...</option>
                      {ingredients.map((ing) => (
                        <option key={ing.id} value={ing.id}>
                          {ing.nama} ({ing.berat} {ing.satuan}) - Rp {formatRupiah(ing.harga)} total
                        </option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Jumlah
                    </label>
                    <input
                      type="number"
                      name="quantity"
                      value={ingredient.quantity}
                      onChange={(e) => handleInputChange(e, index)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Harga
                    </label>
                    <div className="mt-1 text-sm text-gray-600">
                      {ingredient.ingredientId && (
                        <span>Rp {formatRupiah(ingredient.harga)}</span>
                      )}
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Satuan
                    </label>
                    <select
                      name="unit"
                      value={ingredient.unit}
                      onChange={(e) => handleInputChange(e, index)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      {units.map((unit) => (
                        <option key={unit.value} value={unit.value}>
                          {unit.label}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div className="flex flex-col gap-2 justify-end">
                    <ModernButton
  onClick={() => removeIngredient(index)}
  color="error"
  size="small"
  style={{ borderRadius: 10, fontWeight: 600, minHeight: 36, minWidth: 36, fontSize: 15, padding: 0 }}
  startIcon={<Trash2 className="w-4 h-4" />} 
  title="Hapus bahan baku"
>
  Hapus
</ModernButton>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Add Ingredient Button */}
        <div className="px-6 py-3 border-t border-gray-200">
          <ModernButton
            onClick={addIngredient}
            color="primary"
            size="medium"
            fontWeight={600}
            startIcon={<Plus className="w-4 h-4" />}
          >
            Tambah Bahan Baku
          </ModernButton>
        </div>

        {/* Fixed Footer with Action Buttons */}
        <div className="border-t p-4 bg-gray-50">
          <div className="flex justify-end gap-3">
            <ModernButton
              onClick={onClose}
              color="inherit"
              size="medium"
              fontWeight={600}
              style={{ borderRadius: 10, fontWeight: 600, minWidth: 90, minHeight: 40 }}
            >
              Batal
            </ModernButton>
            <ModernButton
              onClick={saveHPP}
              color="primary"
              size="medium"
              style={{ borderRadius: 10, fontWeight: 700, minWidth: 120, minHeight: 40, padding: '10px 20px' }}
            >
              {editingRecord ? 'Simpan Perubahan' : 'Tambah HPP'}
            </ModernButton>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HPPModal;
