import React, { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { FileText, Eye, Download, Edit, Image, AlertCircle, Trash2 } from 'lucide-react'
import { getInvoices, getCustomers, updateInvoice, deleteInvoice, getProducts, initializeSampleData } from '../services/storage'
import { generatePDF } from '../services/pdfService'
import { generateInvoiceImage } from '../services/imageService'
import CreateInvoiceButton from '../components/CreateInvoiceButton'
import { useGlobalDialog } from '../contexts/DialogContext'
import { getCurrentTheme, themes } from '../services/themeService'
import ModernButton from '../components/ModernButton'

const Invoices = () => {
  const [invoices, setInvoices] = useState([])
  const [customers, setCustomers] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [activeTab, setActiveTab] = useState('draft')
  const { deleteConfirm, success, error: showError } = useGlobalDialog()
  const currentTheme = getCurrentTheme()
  const themeColor = themes[currentTheme]?.primary || 'emerald'

  useEffect(() => {
    loadData()
  }, [])

  const loadData = () => {
    try {
      // Initialize sample data if no data exists
      const existingInvoices = getInvoices()
      const existingCustomers = getCustomers()

      if (existingInvoices.length === 0 && existingCustomers.length === 0) {
        initializeSampleData()
      }

      const invoiceData = getInvoices()
      const customerData = getCustomers()

      // Sort items in each invoice by date
      const invoicesWithSortedItems = invoiceData.map(invoice => ({
        ...invoice,
        items: [...invoice.items].sort((a, b) => new Date(a.tanggal) - new Date(b.tanggal))
      }))

      setInvoices(invoicesWithSortedItems)
      setCustomers(customerData)
      setError(null)
    } catch (err) {
      setError('Terjadi kesalahan saat memuat data')
      console.error('Error loading data:', err)
    } finally {
      setLoading(false)
    }
  }

  const getCustomerName = (customerId) => {
    const customer = customers.find(c => c.id === customerId)
    return customer ? customer.nama : 'Unknown'
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR'
    }).format(amount)
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('id-ID')
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'draft':
        return 'bg-gray-400'
      case 'paid':
        return 'bg-green-500'
      default:
        return 'bg-gray-400'
    }
  }

  const getStatusText = (status) => {
    switch (status) {
      case 'paid':
        return 'Lunas'
      case 'sent':
        return 'Terkirim'
      default:
        return 'Draft'
    }
  }

  const handleStatusChange = (invoiceId, newStatus) => {
    updateInvoice(invoiceId, { status: newStatus })
    loadData()
  }

  const handleDeleteInvoice = async (invoice) => {
    const customerName = getCustomerName(invoice.pelangganId)

    const confirmed = await deleteConfirm({
      title: 'Hapus Invoice',
      message: `Apakah Anda yakin ingin menghapus invoice ${invoice.nomorInvoice} untuk ${customerName}?\n\nTindakan ini tidak dapat dibatalkan.`,
      confirmText: 'Hapus',
      cancelText: 'Batal'
    })

    if (confirmed) {
      try {
        const deleteSuccess = deleteInvoice(invoice.id)
        if (deleteSuccess) {
          await success({
            title: 'Berhasil!',
            message: 'Invoice berhasil dihapus!'
          })
          loadData() // Refresh the invoice list
        } else {
          await showError({
            title: 'Gagal!',
            message: 'Gagal menghapus invoice. Silakan coba lagi.'
          })
        }
      } catch (error) {
        console.error('Error deleting invoice:', error)
        await showError({
          title: 'Error!',
          message: 'Terjadi kesalahan saat menghapus invoice.'
        })
      }
    }
  }

  const handleDownloadPDF = async (invoice) => {
    const customer = customers.find(c => c.id === invoice.pelangganId)
    await generatePDF(invoice, customer)
  }

  const handleDownloadImage = async (invoice) => {
    const customer = customers.find(c => c.id === invoice.pelangganId)
    const tempDiv = document.createElement('div')
    tempDiv.id = 'temp-invoice-content'
    tempDiv.style.position = 'absolute'
    tempDiv.style.left = '-9999px'
    tempDiv.style.width = '800px'
    tempDiv.style.backgroundColor = 'white'
    tempDiv.style.padding = '20px'
    tempDiv.style.fontFamily = 'Inter, system-ui, sans-serif'

    tempDiv.innerHTML = `
      <div style="border: 1px solid #e5e7eb; border-radius: 12px; overflow: hidden; background: white;">
        <!-- Header -->
        <div style="padding: 24px; border-bottom: 1px solid #f3f4f6; background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);">
          <div style="display: flex; justify-content: space-between; align-items: start;">
            <div style="text-align: left;">
              <h1 style="font-size: 28px; font-weight: bold; color: #059669; margin: 0 0 12px 0;">ROTI RAGIL</h1>
              <div style="margin: 8px 0;">
                <p style="font-size: 16px; font-weight: 600; color: #1f2937; background: #fef3c7; padding: 6px 16px; border-radius: 8px; border: 1px solid #f59e0b; display: inline-block;">No. P-IRT: 2053471011676-30</p>
              </div>
              <p style="font-size: 15px; font-weight: 500; color: #374151; margin: 8px 0 0 0; text-align: left;">Telp: 0895402652626</p>
            </div>
            <div style="text-align: right;">
              <h2 style="font-size: 24px; font-weight: bold; color: #374151; margin: 0 0 8px 0;">INVOICE</h2>
              <p style="font-size: 14px; color: #6b7280; margin: 0;">No: ${invoice.nomorInvoice}</p>
              <p style="font-size: 14px; color: #6b7280; margin: 0;">Tanggal: ${formatDate(invoice.tanggal)}</p>
            </div>
          </div>
        </div>

        <!-- Customer -->
        <div style="padding: 24px; border-bottom: 1px solid #f3f4f6;">
          <h3 style="font-size: 18px; font-weight: 600; color: #374151; margin: 0 0 12px 0;">Kepada:</h3>
          <div style="background: #f9fafb; padding: 16px; border-radius: 8px;">
            <p style="font-size: 18px; font-weight: 600, color: #374151; margin: 0;">${customer ? customer.nama : 'Unknown'}</p>
          </div>
        </div>

        <!-- Items -->
        <div style="padding: 24px;">
          <table style="width: 100%; border-collapse: collapse;">
            <thead>
              <tr style="background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);">
                <th style="padding: 12px; text-align: left; font-size: 12px; font-weight: 500; color: #6b7280; text-transform: uppercase;">No</th>
                <th style="padding: 12px; text-align: left; font-size: 12px; font-weight: 500; color: #6b7280; text-transform: uppercase;">Produk</th>
                <th style="padding: 12px; text-align: center; font-size: 12px; font-weight: 500; color: #6b7280; text-transform: uppercase;">Tanggal</th>
                <th style="padding: 12px; text-align: center; font-size: 12px; font-weight: 500; color: #6b7280; text-transform: uppercase;">Qty</th>
                <th style="padding: 12px; text-align: right; font-size: 12px; font-weight: 500; color: #6b7280; text-transform: uppercase;">Harga</th>
                <th style="padding: 12px; text-align: right; font-size: 12px; font-weight: 500; color: #6b7280; text-transform: uppercase;">Total</th>
              </tr>
            </thead>
            <tbody>
              ${invoice.items.map((item, index) => {
                const productsData = getProducts()
                const product = productsData.find(p => p.id === item.produkId)
                const productName = product ? product.nama : 'Produk Tidak Ditemukan'
                return `
                  <tr style="border-bottom: 1px solid #f3f4f6;">
                    <td style="padding: 16px; font-size: 14px; font-weight: 500; color: #374151;">${index + 1}</td>
                    <td style="padding: 16px; font-size: 14px; font-weight: 500; color: #374151;">${productName}</td>
                    <td style="padding: 16px; font-size: 14px; color: #6b7280; text-align: center;">${item.tanggal ? new Date(item.tanggal).toLocaleDateString('id-ID') : '-'}</td>
                    <td style="padding: 16px; font-size: 14px; font-weight: 500; color: #374151; text-align: center;">${item.quantity}</td>
                    <td style="padding: 16px; font-size: 14px; color: #374151; text-align: right;">${formatCurrency(item.harga)}</td>
                    <td style="padding: 16px; font-size: 14px; font-weight: 600; color: #374151; text-align: right;">${formatCurrency(item.quantity * item.harga)}</td>
                  </tr>
                `
              }).join('')}
            </tbody>
          </table>
        </div>

        <!-- Total -->
        <div style="padding: 24px; border-top: 1px solid #f3f4f6; background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <div>
              <p style="font-size: 14px; font-weight: 500; color: #374151; margin: 0;">Terima kasih atas kepercayaan Anda!</p>
              <p style="font-size: 14px; color: #6b7280; margin: 0;">Pembayaran dapat dilakukan melalui transfer bank atau tunai.</p>
            </div>
            <div style="text-align: right;">
              <p style="font-size: 14px; color: #6b7280; margin: 0 0 4px 0;">Total Pembayaran</p>
              <p style="font-size: 24px; font-weight: bold; color: #059669; margin: 0;">${formatCurrency(invoice.total)}</p>
            </div>
          </div>
        </div>
      </div>
    `

    document.body.appendChild(tempDiv)

    try {
      await generateInvoiceImage(invoice, customer, 'temp-invoice-content')
    } finally {
      document.body.removeChild(tempDiv)
    }
  }

  const getFilteredInvoices = () => {
    switch (activeTab) {
      case 'draft':
        return invoices.filter(invoice => invoice.status === 'draft')
      case 'sent':
        return invoices.filter(invoice => invoice.status === 'sent')
      case 'paid':
        return invoices.filter(invoice => invoice.status === 'paid')
      default:
        return invoices
    }
  }

  if (loading) {
    return (
      <div className="h-[80vh] flex flex-col items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-500 mb-4"></div>
        <p className="text-gray-600 font-medium">Memuat invoice...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className="h-[60vh] flex flex-col items-center justify-center">
        <div className="bg-red-50 rounded-full p-3 mb-4">
          <AlertCircle className="h-8 w-8 text-red-500" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Gagal memuat data</h3>
        <p className="text-gray-600 mb-4">{error}</p>
        <button
          onClick={loadData}
          className="inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-emerald-600 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500"
        >
          Coba Lagi
        </button>
      </div>
    )
  }

  const filteredInvoices = getFilteredInvoices()

  return (
    <div className="max-w-[100vw] overflow-hidden">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6 flex justify-between items-center">
          <h1 className="text-2xl font-bold text-gray-900">Daftar Invoice</h1>
          <CreateInvoiceButton />
        </div>

        {/* Tab Navigation */}
        <div className="mb-6 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <div className="flex space-x-4">
              <button
                onClick={() => setActiveTab('draft')}
                className={`${
                  activeTab === 'draft'
                    ? `border-${themeColor}-500 text-${themeColor}-600`
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
              >
                Draft
              </button>
              <button
                onClick={() => setActiveTab('paid')}
                className={`${
                  activeTab === 'paid'
                    ? `border-${themeColor}-500 text-${themeColor}-600`
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
              >
                Lunas
              </button>
            </div>
            <button
              onClick={() => setActiveTab('all')}
              className={`${
                activeTab === 'all'
                  ? `border-${themeColor}-500 text-${themeColor}-600`
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm`}
            >
              Semua
            </button>
          </div>
        </div>

        {error && (
          <div className="mb-4 p-4 bg-red-50 rounded-lg flex items-center text-red-800 gap-3">
            <AlertCircle className="h-5 w-5" />
            <p>{error}</p>
          </div>
        )}

        {loading ? (
          <div className="text-center py-16">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-4 border-emerald-500 border-r-transparent"></div>
          </div>
        ) : invoices.length > 0 ? (
          <>
            {/* Desktop Table */}
            <div className="hidden md:block">
              <div className="overflow-hidden rounded-lg ring-1 ring-gray-300">
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-300">
                    <thead className="bg-gray-50">
                      <tr>
                        <th scope="col" className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">
                          No. Invoice
                        </th>
                        <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                          Pelanggan
                        </th>
                        <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                          Tanggal
                        </th>
                        <th scope="col" className="px-3 py-3.5 text-right text-sm font-semibold text-gray-900">
                          Total
                        </th>
                        <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                          Status
                        </th>
                        <th scope="col" className="relative py-3.5 pl-3 pr-4 sm:pr-6">
                          <span className="sr-only">Actions</span>
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200 bg-white">
                      {filteredInvoices.map((invoice) => (
                        <tr key={invoice.id} className="hover:bg-gray-50 transition-colors">
                          <td className="whitespace-nowrap px-3 py-4 text-sm font-medium text-gray-900">
                            {getCustomerName(invoice.pelangganId) || 'Pelanggan tidak ditemukan'}
                          </td>
                          <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-900">
                            {formatDate(invoice.tanggal)}
                          </td>
                          <td className="whitespace-nowrap px-3 py-4 text-sm font-semibold text-emerald-600 text-right">
                            {formatCurrency(invoice.total)}
                          </td>
                          <td className="whitespace-nowrap px-3 py-4">
                            <div className="relative group">
                              <div className={`w-3 h-3 rounded-full ${getStatusColor(invoice.status)}`} title={invoice.status === 'paid' ? 'Lunas' : 'Draft'}></div>
                              <select
                                value={invoice.status}
                                onChange={(e) => handleStatusChange(invoice.id, e.target.value)}
                                className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                              >
                                <option value="draft">Draft</option>
                                <option value="paid">Lunas</option>
                              </select>
                            </div>
                          </td>
                          <td className="whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                            <div className="flex justify-end items-center gap-1">
                              <Link
                                to={`/invoices/${invoice.id}`}
                                className="text-emerald-600 dark:text-emerald-400 hover:text-emerald-900 dark:hover:text-emerald-300 p-1.5 rounded-full hover:bg-emerald-100 dark:hover:bg-emerald-900/30 transition-colors"
                                title="Lihat Detail"
                              >
                                <Eye className="h-4 w-4" />
                              </Link>
                              <Link
                                to={`/invoices/${invoice.id}/edit`}
                                className="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 p-1.5 rounded-full hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors"
                                title="Edit Invoice"
                              >
                                <Edit className="h-4 w-4" />
                              </Link>
                              <button
                                onClick={() => handleDownloadImage(invoice)}
                                className="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 p-1.5 rounded-full hover:bg-indigo-100 dark:hover:bg-indigo-900/30 transition-colors"
                                title="Download Gambar"
                              >
                                <Image className="h-4 w-4" />
                              </button>
                              <button
                                onClick={() => handleDeleteInvoice(invoice)}
                                className="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 p-1.5 rounded-full hover:bg-red-100 dark:hover:bg-red-900/30 transition-colors"
                                title="Hapus Invoice"
                              >
                                <Trash2 className="h-4 w-4" />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            {/* Mobile Table - Single Line */}
            <div className="md:hidden space-y-2">
              {filteredInvoices.map((invoice) => (
                <div key={invoice.id} className="bg-white rounded-lg p-3 border border-gray-200 flex items-center justify-between gap-2">
                  <div className="flex-1 min-w-0 flex items-center gap-3 overflow-hidden">
                    <div className="flex-shrink-0">
                      <div className="relative group">
                        <div className={`w-2.5 h-2.5 rounded-full ${getStatusColor(invoice.status)}`} title={invoice.status === 'paid' ? 'Lunas' : 'Draft'}></div>
                        <select
                          value={invoice.status}
                          onChange={(e) => handleStatusChange(invoice.id, e.target.value)}
                          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                        >
                          <option value="draft">Draft</option>
                          <option value="paid">Lunas</option>
                        </select>
                      </div>
                    </div>
                    <div className="min-w-0">
                      <div className="text-sm font-medium text-gray-900 truncate">
                        {getCustomerName(invoice.pelangganId) || 'Pelanggan tidak ditemukan'}
                      </div>
                      <div className="text-xs text-gray-600">
                        {formatDate(invoice.tanggal)}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-1">
                    <span className="font-semibold text-emerald-600 whitespace-nowrap mr-1">
                      {formatCurrency(invoice.total)}
                    </span>
                    <div className="flex items-center">
                      <Link
                        to={`/invoices/${invoice.id}`}
                        className="text-emerald-600 dark:text-emerald-400 hover:text-emerald-900 dark:hover:text-emerald-300 p-1.5 rounded-full hover:bg-emerald-100 dark:hover:bg-emerald-900/30 transition-colors"
                        title="Lihat Detail"
                      >
                        <Eye className="h-4 w-4" />
                      </Link>
                      <Link
                        to={`/invoices/${invoice.id}/edit`}
                        className="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 p-1.5 rounded-full hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors"
                        title="Edit"
                      >
                        <Edit className="h-4 w-4" />
                      </Link>
                      <button
                        onClick={() => handleDownloadImage(invoice)}
                        className="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 p-1.5 rounded-full hover:bg-indigo-100 dark:hover:bg-indigo-900/30 transition-colors"
                        title="Gambar"
                      >
                        <Image className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteInvoice(invoice)}
                        className="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 p-1.5 rounded-full hover:bg-red-100 dark:hover:bg-red-900/30 transition-colors"
                        title="Hapus"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </>
        ) : (
          <div className="text-center py-16 px-4">
            <div className="bg-emerald-50 rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
              <FileText className="h-8 w-8 text-emerald-500" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {activeTab === 'all' 
                ? 'Belum ada invoice' 
                : `Belum ada invoice dengan status ${
                    activeTab === 'paid' ? 'Lunas' : 
                    activeTab === 'sent' ? 'Terkirim' : 
                    'Draft'
                  }`
              }
            </h3>
            <p className="text-gray-600 max-w-sm mx-auto mb-8">
              Mulai dengan membuat invoice pertama Anda untuk mengelola transaksi penjualan dengan lebih baik.
            </p>
            <div className="flex justify-center">
              <CreateInvoiceButton
                variant="primary"
                size="large"
                iconType="create"
                className="w-full sm:w-auto"
              />
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default Invoices
