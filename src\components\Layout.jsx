import React, { useState, useEffect } from 'react'
import { Link, useLocation } from 'react-router-dom'
import {
  Home,
  Package,
  Users,
  FileText,
  BarChart3,
  Info,
  Menu,
  Calculator,
  Settings
} from 'lucide-react'
import Container from '@mui/material/Container'
import Drawer from '@mui/material/Drawer'
import AppBar from '@mui/material/AppBar'
import Toolbar from '@mui/material/Toolbar'
import Typography from '@mui/material/Typography'
import Box from '@mui/material/Box'
import IconButton from '@mui/material/IconButton'
import { useTheme } from '@mui/material/styles'
import useMediaQuery from '@mui/material/useMediaQuery'
import WheatIcon from './WheatIcon'
import CreateInvoiceButton from './CreateInvoiceButton'
import ThemeSelector from './ThemeSelector'
import { getCurrentTheme, getThemeConfig } from '../services/themeService'
import FontSwitcher from './FontSwitcher'

const FONT_STORAGE_KEY = 'roti-ragil-font-family';

const Layout = ({ children }) => {
  const [mobileOpen, setMobileOpen] = useState(false)
  const [fontFamily, setFontFamily] = useState(() => {
    return localStorage.getItem(FONT_STORAGE_KEY) || 'poppins';
  });
  const [hovered, setHovered] = useState(null);
  const location = useLocation()

  useEffect(() => {
    // Apply selected theme
    const applyTheme = () => {
      const themeName = getCurrentTheme();
      const theme = getThemeConfig(themeName);
      const root = document.documentElement;
      root.style.setProperty('--primary-color', theme.primary);
      root.style.setProperty('--secondary-color', theme.secondary);
      root.style.setProperty('--sidebar-bg', theme.sidebarBg);
      root.style.setProperty('--sidebar-active-bg', 'rgba(16,24,32,0.08)');
      root.style.setProperty('--button-bg', theme.buttonBg);
      root.style.setProperty('--button-color', theme.buttonColor);
      root.style.setProperty('--button-hover-bg', theme.buttonHoverBg);
      root.style.setProperty('--button-active-bg', theme.buttonActiveBg);
      root.className = '';
      root.classList.add(`theme-${themeName}`);
    };
    applyTheme();

    // Listen for theme changes (from ThemeSelector or elsewhere)
    window.handleThemeChange = () => {
      applyTheme();
    };
    window.addEventListener('storage', applyTheme);
    return () => {
      window.removeEventListener('storage', applyTheme);
    };
  }, []);

  useEffect(() => {
    // Set font class on body
    document.body.classList.remove('font-inter', 'font-nunito', 'font-poppins', 'font-quicksand', 'font-worksans');
    document.body.classList.add(`font-${fontFamily}`);
    localStorage.setItem(FONT_STORAGE_KEY, fontFamily);
  }, [fontFamily]);

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen)
  }

  const navigation = [
    { name: 'Dashboard', href: '/', icon: Home },
    { name: 'Produk', href: '/products', icon: Package },
    { name: 'Daftar Harga', href: '/price-list', icon: BarChart3 },
    { name: 'Pelanggan', href: '/customers', icon: Users },
    { name: 'Invoice', href: '/invoices', icon: FileText },
    { name: 'Pengeluaran', href: '/expenses', icon: FileText },
    { name: 'Laporan', href: '/reports', icon: BarChart3 },
    { name: 'Bahan Baku', href: '/bahan-baku', icon: Package },
    { name: 'HPP', href: '/hpp', icon: Calculator },
    { name: 'Tentang', href: '/about', icon: Info },
    { name: 'Settings', href: '/settings', icon: Settings },
  ]

  const isActive = (path) => {
    return location.pathname === path
  }

  // Drawer content component
  const drawerContent = (
    <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
      <Box sx={{ mb: 2, py: 2, px: 3, display: 'flex', alignItems: 'center', gap: 2 }}>
        <WheatIcon sx={{ fontSize: 32, color: 'var(--theme-500)' }} />
        <Typography variant="h6" sx={{ fontWeight: 700, color: 'rgb(147, 51, 234)' }}>
          Roti Ragil
        </Typography>
      </Box>
      <Box sx={{ flex: 1, overflow: 'auto', px: 2 }}>
        <nav>
          {navigation.map((item) => (
            <Link
              key={item.href}
              to={item.href}
              style={{ textDecoration: 'none' }}
              onClick={() => isMobile && setMobileOpen(false)} // Close mobile drawer on navigation
            >
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  px: 2,
                  py: 1.5,
                  borderRadius: 2,
                  mb: 1,
                  minHeight: 44, // Touch-friendly minimum height
                  background: isActive(item.href)
                    ? 'rgba(0,0,0,0.08)'
                    : (hovered === item.href ? 'rgba(0,0,0,0.08)' : 'rgba(255,255,255,0.00)'),
                  color: isActive(item.href) ? 'var(--theme-700)' : 'var(--theme-700,#334155)',
                  fontWeight: isActive(item.href) ? 700 : 500,
                  transition: 'all 0.2s ease',
                  boxShadow: isActive(item.href)
                    ? '0 4px 20px rgba(147,51,234,0.12)'
                    : 'none',
                }}
                onMouseEnter={() => setHovered(item.href)}
                onMouseLeave={() => setHovered(null)}
              >
                <item.icon style={{ marginRight: 12, color: isActive(item.href) ? 'var(--theme-700)' : 'var(--theme-700,#334155)', fontSize: 22 }} />
                <span style={{ color: isActive(item.href) ? 'var(--theme-700)' : 'var(--theme-700,#334155)', fontWeight: 600 }}>{item.name}</span>
              </Box>
            </Link>
          ))}
        </nav>
      </Box>

      <Box sx={{ p: 2, borderTop: '1px solid var(--theme-100)' }}>
        <Typography variant="caption" color="text.secondary" align="center">
          © {new Date().getFullYear()} Roti Ragil
        </Typography>
      </Box>
    </Box>
  )

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh', bgcolor: 'var(--theme-50)' }}>
      {/* AppBar for top navigation */}
      <AppBar
        position="fixed"
        sx={{
          background: 'var(--theme-100, #fff)',
          color: 'var(--theme-700)',
          boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
          width: { md: `calc(100% - 240px)` },
          ml: { md: `240px` },
          zIndex: (theme) => theme.zIndex.drawer + 1,
          borderRadius: '0 0 20px 20px',
        }}
      >
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ mr: 2, display: { md: 'none' }, borderRadius: '12px', background: 'var(--theme-100, #fff)' }}
          >
            <Menu style={{ color: 'var(--theme-700)' }} />
          </IconButton>
          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
            {/* You can add page title here if needed */}
          </Typography>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <ThemeSelector />
          </Box>
        </Toolbar>
      </AppBar>
      {/* Mobile drawer */}
      <Drawer
        variant="temporary"
        open={mobileOpen}
        onClose={handleDrawerToggle}
        ModalProps={{
          keepMounted: true, // Better open performance on mobile
        }}
        sx={{
          display: { xs: 'block', md: 'none' },
          '& .MuiDrawer-paper': {
            width: 240,
            boxSizing: 'border-box',
            background: 'var(--theme-100, #fff)',
            borderRight: '1px solid var(--theme-200, #e5e7eb)',
            boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
            borderRadius: '0 20px 20px 0',
          },
        }}
      >
        {drawerContent}
      </Drawer>

      {/* Desktop drawer */}
      <Drawer
        variant="permanent"
        sx={{
          display: { xs: 'none', md: 'block' },
          width: 240,
          flexShrink: 0,
          '& .MuiDrawer-paper': {
            width: 240,
            boxSizing: 'border-box',
            background: 'var(--theme-100, #fff)',
            borderRight: '1px solid var(--theme-200, #e5e7eb)',
            boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
            borderRadius: '0 20px 20px 0',
          },
        }}
        open
      >
        {drawerContent}
      </Drawer>
      {/* Main content */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          width: { md: `calc(100% - 240px)` },
          ml: { md: `240px` },
          pt: { xs: 7, md: 8 }, // Account for AppBar height
          bgcolor: 'var(--theme-50)',
          minHeight: '100vh',
          transition: 'margin 0.3s ease', // Smooth transition
        }}
      >
        <Container
          maxWidth="lg"
          sx={{
            py: { xs: 2, md: 4 }, // Responsive padding
            px: { xs: 2, md: 3 }, // Responsive horizontal padding
          }}
        >
          {children}
        </Container>
      </Box>
    </Box>
  )
}

export default Layout
